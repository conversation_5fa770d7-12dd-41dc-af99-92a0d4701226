clc; clear; close all;
warning('off', 'MATLAB:table:ModifiedVarnames');

%% 批量处理参数配置
input_folder = 'C:\Users\<USER>\Desktop\snow\tu';  % 输入文件夹路径

%% EEMD特有参数配置
eemd_params.num_ensemble = 100;     % 集成次数（建议100-500）
eemd_params.noise_width = 0.2;      % 噪声幅度比例（信号标准差的倍数）
min_delta = 1e-9;                                % 严格递增处理参数

%% 初始化环境
excel_files = dir(fullfile(input_folder, '*.xlsx'));

%% 批量处理循环
for fileIdx = 1:length(excel_files)
    current_filename = fullfile(input_folder, excel_files(fileIdx).name);
    [~, workbook_name, ~] = fileparts(current_filename);
    
    fprintf('\n开始处理工作簿: %d/%d | 工作簿名: %s\n', fileIdx, length(excel_files), workbook_name);
    
    [~, sheetList] = xlsfinfo(current_filename);
    results = struct('SheetName', {}, 'MainFreq', {}, 'StartTime', {}, 'EndTime', {});
    
    %% 工作表处理循环
    for sheetIdx = 1:length(sheetList)
        currentSheet = sheetList{sheetIdx};
        fprintf('  处理工作表: %d/%d | 工作表名: %s\n', sheetIdx, length(sheetList), currentSheet);
        
        try 
            %% 数据读取
            opts = detectImportOptions(current_filename, 'Sheet', currentSheet);
            opts.SelectedVariableNames = {'TIME', 'EL_deg', 'SNR_dBHz'};  
            rawData = readtable(current_filename, opts);
            
            %% 数据预处理
            [sinEL, snr_volts, time_valid] = preprocessData(rawData.TIME, rawData.EL_deg, rawData.SNR_dBHz);
            start_time = min(time_valid);
            end_time = max(time_valid);
            
            %% 核心分析（使用EEMD）
            [zhupin, filtered_signal, sinEL_sorted, imfs, residue] = coreAnalysisEEMD(...
                sinEL, snr_volts, min_delta, eemd_params);
            
            %% 结果存储
            results(sheetIdx) = struct( ...
                'SheetName', currentSheet, ...
                'MainFreq', zhupin, ...
                'StartTime', start_time, ...
                'EndTime', end_time ...
            );
            
            %% 生成图表
            if ~isempty(sinEL_sorted) && ~isempty(filtered_signal)
                disp(['sinEL_sorted长度: ', num2str(length(sinEL_sorted))]);
                disp(['filtered_signal长度: ', num2str(length(filtered_signal))]);
                generateSpectrumPlot(currentSheet, sinEL_sorted, filtered_signal, snr_volts);
            end
            if ~isempty(imfs) && ~isempty(residue)
                disp(['imfs大小: ', num2str(size(imfs))]);
                disp(['residue长度: ', num2str(length(residue))]);
                generateEMDPlot(currentSheet, imfs, residue, sinEL_sorted, snr_volts);  
            end
            
        catch ME 
            % 显示详细错误信息，包括调用栈
            disp(['错误发生在工作表: ' currentSheet]);
            disp(ME.message);
            disp(ME.stack);  
            logError(currentSheet, ME.message); 
            results(sheetIdx) = struct( ...
                'SheetName', currentSheet, ...
                'MainFreq', NaN, ...
                'StartTime', NaT, ...
                'EndTime', NaT ...
            );
        end 
    end 
    
    fprintf('  工作簿处理完成: %s\n', workbook_name);
end 

%% 数据预处理函数（保持不变）
function [sinEL, snr_volts, time_valid] = preprocessData(Time, EL_deg, SNR_dBHz)
    if ~isdatetime(Time)
        try
            Time = datetime(Time, 'ConvertFrom', 'excel');
        catch
            error('时间列格式无法解析');
        end
    end
    validIdx = ~isnat(Time) & ~isnan(EL_deg) & ~isnan(SNR_dBHz);
    if sum(validIdx) < 2
        error('有效数据不足（至少需要2个点）');
    end
    sinEL = sind(EL_deg(validIdx));
    snr_volts = 10.^(SNR_dBHz(validIdx)/20);
    time_valid = Time(validIdx);
    
    % 统一排序处理
    [sinEL, sortIdx] = sort(sinEL);
    snr_volts = snr_volts(sortIdx);
    time_valid = time_valid(sortIdx);
end

%% 核心分析函数（修改为EEMD）
function [zhupin, filtered_signal, sinEL_sorted, imfs, residue] = coreAnalysisEEMD(...
    sinEL, snr_volts, min_delta, eemd_params)
    
    zhupin = NaN;
    filtered_signal = [];
    sinEL_sorted = [];
    imfs = [];
    residue = [];
    
    try
        %% 严格递增处理
        sinEL_sorted = makeStrictlyIncreasing(sinEL, min_delta);
        
        %% 验证数据排序
        assert(issorted(sinEL_sorted), 'sinEL必须严格递增排序');
        assert(all(diff(sinEL_sorted) > 0), 'sinEL必须严格递增');
        
        %% EEMD分解原始信号
        [imfs, residue] = custom_eemd(snr_volts, eemd_params.num_ensemble, eemd_params.noise_width);
        
        %% 筛选有效IMF分量
        if isempty(imfs)
            error('EEMD分解未产生有效IMF分量');
        end
        
        % 计算每个IMF与总和的相关系数
        sum_imfs = sum(imfs, 2);
        correlations = arrayfun(@(i) corr(imfs(:,i), sum_imfs), 1:size(imfs,2));
        valid_imfs = imfs(:, correlations > 0.5);
        
        %% 重构信号
        if isempty(valid_imfs)
            filtered_signal = sum_imfs; % 使用全部IMF
        else
            filtered_signal = sum(valid_imfs, 2);
        end
        
        %% 频谱分析
        f_custom = 0:0.1:400;
        [P, f] = plomb(filtered_signal, sinEL_sorted, f_custom);
        [~, maxIdx] = max(P);
        zhupin = f(maxIdx);
        
    catch ME
        disp(['分析错误: ' ME.message]);
    end
end

%% 严格递增处理函数（保持不变）
function x = makeStrictlyIncreasing(x, minDelta)
    for i = 2:length(x)
        if x(i) <= x(i-1)
            requiredDelta = x(i-1) - x(i) + minDelta;
            x(i) = x(i) + requiredDelta;
        end
    end
end

%% 自定义EEMD实现
function [imfs, residue] = custom_eemd(signal, num_ensemble, noise_width)
    % 参数说明：
    % signal: 输入信号
    % num_ensemble: 集成次数（默认100）
    % noise_width: 噪声幅度比例（默认0.2）
    
    if nargin < 2, num_ensemble = 100; end
    if nargin < 3, noise_width = 0.2; end
    
    n = length(signal);
    signal_std = std(signal);
    noise_std = noise_width * signal_std;
    
    % 预分配存储
    max_imfs = 0;
    imfs_ensemble = cell(num_ensemble, 1);
    residue_ensemble = zeros(n, num_ensemble);
    
    for i = 1:num_ensemble
        % 添加高斯白噪声
        noisy_signal = signal + noise_std * randn(size(signal));
        
        % 执行EMD分解
        [current_imfs, current_residue] = emd(noisy_signal, 'Interpolation', 'pchip');
        
        % 记录最大IMF数量
        max_imfs = max(max_imfs, size(current_imfs, 2));
        
        % 存储结果
        imfs_ensemble{i} = current_imfs;
        residue_ensemble(:,i) = current_residue;
    end
    
    % 初始化输出
    imfs = zeros(n, max_imfs);
    
    % 对每个IMF位置取平均
    for j = 1:max_imfs
        imf_avg = zeros(n, 1);
        count = 0;
        for i = 1:num_ensemble
            if size(imfs_ensemble{i}, 2) >= j
                imf_avg = imf_avg + imfs_ensemble{i}(:,j);
                count = count + 1;
            end
        end
        if count > 0
            imfs(:,j) = imf_avg / count;
        else
            imfs(:,j) = 0; % 无有效IMF时置零
        end
    end
    
    % 计算残余平均
    residue = mean(residue_ensemble, 2);
end

%% 频谱图生成函数（保持不变）
function generateSpectrumPlot(sheetName, sinEL_sorted, filtered_signal, original_signal)
    fig = figure('Visible', 'on', 'Position', [100 100 900 800]);
    
    % 原始信号子图
    subplot(3,1,1)
    plot(sinEL_sorted, original_signal, 'b', 'LineWidth', 1)
    title(sprintf('%s 原始信号', sheetName), 'Interpreter', 'none')
    xlabel('sin(EL)'), ylabel('Amplitude (V)')
    grid on
    xlim([min(sinEL_sorted) max(sinEL_sorted)])
    
    % 重构信号子图
    subplot(3,1,2)
    plot(sinEL_sorted, filtered_signal, 'r', 'LineWidth', 1)
    title(sprintf('%s 重构信号', sheetName), 'Interpreter', 'none')
    xlabel('sin(EL)'), ylabel('Amplitude (V)')
    grid on
    xlim([min(sinEL_sorted) max(sinEL_sorted)])
    
    % 频谱分析子图
    subplot(3,1,3)
    f_custom = 0:0.1:400;
    [P, f] = plomb(filtered_signal, sinEL_sorted, f_custom);
    plot(f, P, 'k', 'LineWidth', 1)
    xlim([0 400])
    xlabel('Frequency (Hz)')
    ylabel('Power')
    grid on
    
    set(findall(fig, 'Type', 'axes'), 'FontSize', 9)
    set(fig, 'Color', 'w')
    % 移除saveas和close语句，图形自动保持显示
end

%% EMD可视化函数（修改版）
function generateEMDPlot(sheetName, imfs, residue, sinEL_sorted, original_signal)
     fig_decomposition = figure('Visible', 'on', 'Color', 'w');
 % 增加高度以容纳更多子图
    numIMFs = size(imfs, 2);
    
    % 绘制原始信号（新增子图）
    subplot(numIMFs+2, 1, 1);
    plot(sinEL_sorted, original_signal, 'R', 'LineWidth', 1);
%     title(sprintf('%s 原始信号', sheetName), 'Interpreter', 'none');
%     xlabel('sin(EL)'),
    ylabel('SNR',"FontSize",12);
    grid on;
    xlim([0.05,0.45]);
    set(gca, 'XTickLabel', []); % 不显示当前子图的横坐标刻度
    % 绘制IMF分量（调整子图位置）
    for i = 1:numIMFs
        subplot(numIMFs+2, 1, i+1);
        plot(sinEL_sorted, imfs(:,i), 'black','LineWidth', 0.5);
        ylabel(['IMF ', num2str(i)],"FontSize",12);
        grid on;
        xlim([0.05,0.45]); % 统一X轴范围
    set(gca, 'XTickLabel', []); % 不显示当前子图的横坐标刻度
    end
    
    % 绘制残差（调整子图位置）
    subplot(numIMFs+2, 1, numIMFs+2);
    plot(sinEL_sorted, residue, 'g','LineWidth', 0.5);
    ylabel('RESIDUAL',"FontSize",12);
    xlabel('sinEL',"FontSize",12);
    grid on;
    xlim([0.05,0.45]);
     set(fig_decomposition, 'Position', [100, 10, 900, 600]);
%     sgtitle([sheetName ' - EMD分解结果'], 'Interpreter', 'none');
end

%% 错误日志函数（保持不变）
function logError(sheet, msg)
    fid = fopen('error_log.txt',  'a');
    fprintf(fid, '[%s] %s: %s\n', datestr(now, 'yyyy-mm-dd HH:MM'), sheet, msg);
    fclose(fid);
end