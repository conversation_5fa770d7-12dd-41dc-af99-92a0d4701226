clc; clear; close all;
warning('off', 'MATLAB:table:ModifiedVarnames');

%% 参数配置 
filename = 'g01.xlsx';           % 输入文件路径 
poly_degree = 3;                      % 多项式拟合阶数 
output_folder = 'NoTime_Analysis';     % 结果输出目录 
min_delta = 1e-9;                     % 严格递增处理参数 

%% 初始化环境 
if ~exist(output_folder, 'dir')
    mkdir(output_folder);
end 
[~, sheetList] = xlsfinfo(filename);
results = struct('SheetName', {}, 'MainFreq', {}, 'Residuals', {}, 'StartTime', {}, 'EndTime', {});

%% 主处理循环 
for sheetIdx = 1:length(sheetList)
    currentSheet = sheetList{sheetIdx};
    try 
        fprintf('\n处理进度: %d/%d | 当前工作表: %s\n', sheetIdx, length(sheetList), currentSheet);
        
        % 数据读取 (包含时间列)
        opts = detectImportOptions(filename, 'Sheet', currentSheet);
        opts.SelectedVariableNames = {'TIME', 'EL', 'SNR'};  
        rawData = readtable(filename, opts);
        
        % 数据预处理 
        [sinEL, snr_volts, time_valid] = preprocessData(rawData.TIME, rawData.EL, rawData.SNR);
        start_time = min(time_valid);
        end_time = max(time_valid);
        
        % 核心分析 (接收三个输出参数)
        [zhupin, residuals, sinEL_sorted] = coreAnalysis(sinEL, snr_volts, poly_degree, min_delta);
        
        % 结果存储 
        results(sheetIdx).SheetName = currentSheet;
        results(sheetIdx).MainFreq = zhupin;
        if ~isempty(sinEL_sorted) && ~isempty(residuals)
            results(sheetIdx).Residuals = [sinEL_sorted(:), residuals(:)];
        else
            results(sheetIdx).Residuals = [];
        end
        results(sheetIdx).StartTime = start_time;
        results(sheetIdx).EndTime = end_time;
        
        % 生成图表 (传递 sinEL_sorted)
        if ~isempty(sinEL_sorted) && ~isempty(residuals)
            generateSpectrumPlot(currentSheet, sinEL_sorted, residuals, output_folder);
        end
        
    catch ME 
        logError(output_folder, currentSheet, ME.message); 
        continue
    end 
end 

%% 结果导出 
exportSummary(results, output_folder);
fprintf('\n==== 分析完成 ====\n');

%% 预处理函数 
function [sinEL, snr_volts, time_valid] = preprocessData(Time, EL, SNR)
    % 转换时间格式并过滤无效数据
    if ~isdatetime(Time)
        try
            Time = datetime(Time, 'ConvertFrom', 'excel');
        catch
            error('时间列格式无法解析');
        end
    end
    validIdx = ~isnat(Time) & ~isnan(EL) & ~isnan(SNR);
    if sum(validIdx) < 2
        error('有效数据不足（至少需要2个点）');
    end
    sinEL = sind(EL(validIdx));
    snr_volts = 10.^(SNR(validIdx)/20);
    time_valid = Time(validIdx);
end
%% 核心分析函数（修改 Lomb-Scargle 频率范围）
function [zhupin, residuals, sinEL_sorted] = coreAnalysis(sinEL, snr_volts, poly_degree, min_delta)
    zhupin = NaN;
    residuals = [];
    sinEL_sorted = [];
    try
        if isempty(sinEL) || isempty(snr_volts) || length(sinEL) ~= length(snr_volts)
            error('输入数据不合法');
        end
        
        % 多项式拟合
        coeffs = polyfit(sinEL, snr_volts, poly_degree);
        residuals = snr_volts - polyval(coeffs, sinEL);
        
        % 严格递增处理
        [sinEL_sorted, sortIdx] = sort(sinEL);
        sinEL_sorted = makeStrictlyIncreasing(sinEL_sorted, min_delta);
        residuals = residuals(sortIdx);
        
        % Lomb-Scargle分析（限定0-400Hz）
        f_custom = 0:0.1:400; % 定义频率范围0-400Hz，步长0.1Hz
        [P, f] = plomb(residuals, sinEL_sorted, f_custom); % 传入自定义频率
        
        % 提取主频
        [~, maxIdx] = max(P);
        zhupin = f(maxIdx);
        
    catch
        % 错误处理
    end
end

%% 可视化函数 
function generateSpectrumPlot(sheetName, sinEL_sorted, residuals, output_folder)
    fig = figure('Visible', 'off');
    subplot(2,1,1)
    scatter(sinEL_sorted, residuals, 15, 'filled')
    title(sprintf('%s 残差分布', sheetName), 'Interpreter', 'none')
    xlabel('sin(EL)'), ylabel('Residual (V)')
    
    subplot(2,1,2)
    % 使用 plot 替代 semilogy，并修改 Y 轴标签
    f_custom = 0:0.1:400;
    [P, f] = plomb(residuals, sinEL_sorted,f_custom);
    xlim([0 400]) % 强制显示0-400Hz
    plot(f, P) % 改为线性坐标
    xlabel('Frequency (Hz)')
    ylabel('Power') % 修改标签为 "Power"
    grid on 
    saveas(fig, fullfile(output_folder, sprintf('%s_频谱.png', sheetName)));
    close(fig);
end
%% 辅助函数 
function logError(folder, sheet, msg)
    fid = fopen(fullfile(folder, 'error_log.txt'),  'a');
    fprintf(fid, '[%s] %s: %s\n', datestr(now, 'yyyy-mm-dd HH:MM'), sheet, msg);
    fclose(fid);
end 

%% 导出汇总表函数调整
function exportSummary(results, folder)
    sheetNames = {results.SheetName}';
    mainFreqs = [results.MainFreq]';
    startTimes = {results.StartTime}';
    endTimes = {results.EndTime}';
    T = table(sheetNames, mainFreqs, startTimes, endTimes, ...
        'VariableNames', {'Sheet', 'MainFrequency', 'StartTime', 'EndTime'});
    writetable(T, fullfile(folder, '频率汇总表.csv'));
    save(fullfile(folder, '完整结果.mat'), 'results');
end 

%% 严格递增处理函数
function x = makeStrictlyIncreasing(x, minDelta)
    for i = 2:length(x)
        if x(i) <= x(i-1)
            requiredDelta = x(i-1) - x(i) + minDelta;
            x(i) = x(i) + requiredDelta;
        end
    end
end