﻿#NoEnv
SetTitleMatchMode, 2  ; 模糊匹配窗口标题

; 检查保存目录是否存在，若不存在则创建
if (!FileExist("D:\SNR")) {
    FileCreateDir, D:\SNR
}

; 启动 RTKPLOT
Run, E:\rtklib_2.4.2\rtklib_2.4.2\bin\rtkplot.exe  ; 替换为你的实际路径
WinWaitActive, RTKPLOT,, 10  ; 等待窗口激活（最多10秒）
if ErrorLevel {
    MsgBox, RTKPLOT 启动失败！
    ExitApp
}

; 定义要处理的文件所在目录
fileDir := "D:\SNOW\ab332020"

; 遍历目录下的 .o 文件
Loop, %fileDir%\*.20o
{
    oFilePath := A_LoopFileFullPath
    ; 提取文件名（去除路径和扩展名）
    saveFileName := SubStr(A_LoopFileFullPath, InStr(A_LoopFileFullPath, "\",, -1) + 1, -3)

    ; 构建对应的 .n 文件路径
    nFilePath := SubStr(oFilePath, 1, -1) . "n"

    ; 示例：点击菜单项或按钮
    ; 假设需要点击 "File" -> "Open"
    Send, !f  ; Alt+F 打开文件菜单
    Sleep, 500
    ; 示例：直接发送 Ctrl+O 打开文件对话框
    Send, ^o  ; ^ 代表 Ctrl 键
    Sleep, 1000  ; 等待对话框弹出
    ; 输入 .o 文件路径
    Send, %oFilePath%
    Sleep, 1000 ; 等待输入完成
    Send, {Enter} ; 模拟按下回车键来点击“打开”按钮

    Send, !f  ; Alt+F 打开文件菜单
    Sleep, 2000
    ; 示例：直接发送 Ctrl+n 打开文件对话框
    Send, ^n  ; ^ 代表 Ctrl 键
    Sleep, 1000  ; 等待对话框弹出
    ; 输入 .n 文件路径
    Send, %nFilePath%
    Sleep, 500 ; 等待输入完成
    Send, {Enter} ; 模拟按下回车键来点击“打开”按钮
   
 ; 再次打开 File 菜单
    Send, !f
    Sleep, 5000
    ; 向下移动到第 12 个菜单项
    Loop, 11 {
        Send, {Down}
        Sleep, 1000
    }
    Send, {Enter}
    Sleep, 1000
    ; 构建保存路径及文件名
    savePath := "D:\SNR\" saveFileName ".txt"  ; 这里假设保存文件格式为txt，可按需修改

    ; 输入保存路径
    Send, %savePath%
    Sleep, 500
    ; 选择当前选中的菜单项
    Send, {Enter}
    Sleep, 1000 ; 等待保存操作完成
}