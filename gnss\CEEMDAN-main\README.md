# CEEMDAN
A MATLAB package for CEEMDAN (Complete Ensemble Empirical Mode Decomposition with Adaptive Noise).

The current is an improved version, introduced in:

[1] Colominas MA, <PERSON><PERSON><PERSON>ha<PERSON> G, Torres ME. "Improve complete ensemble EMD: A suitable tool for biomedical signal processing" 
       Biomedical Signal Processing and Control vol. 14 pp. 19-29 (2014)

The CEEMDAN algorithm was first introduced at ICASSP 2011, Prague, Czech Republic

The authors will be thankful if the users of this code reference the work
where the algorithm was first presented:

[2] Torres ME, Colominas MA, Schlotthauer G, <PERSON><PERSON><PERSON>. "A Complete Ensemble Empirical Mode Decomposition with Adaptive Noise"
       Proc. 36th Int. Conf. on Acoustics, Speech and Signa Processing ICASSP 2011 (May 22-27, Prague, Czech Republic)

Author: <PERSON><PERSON>omi<PERSON>
contact: <EMAIL>
Last version: 25 feb 2015

WARNING: for this code works it is necessary to include in the same
directoy the file emd.m developed by Rilling and Flandrin.
This file is available at %http://perso.ens-lyon.fr/patrick.flandrin/emd.html
We use the default stopping criterion.
We use the last modification: 3.2007
