% 读取原始Excel数据
filename = 'umit.xlsx'; 
data = readtable(filename, 'Sheet', 'Sheet1', 'PreserveVariableNames', true);

% 确保时间列存在且命名为TIME（兼容大小写）
if ismember('Time', data.Properties.VariableNames)
    data.Properties.VariableNames{'Time'} = 'TIME'; % 统一改为大写
end

% 检查TIME列是否为datetime类型
if ~isdatetime(data.TIME)
    data.TIME = datetime(data.TIME, 'InputFormat', 'yyyy-MM-dd HH:mm:ss');
end

% 获取唯一SAT值 
sat_values = unique(data.SAT);

% 创建新Excel文件（覆盖前先检查）
output_filename = 'umi_processed.xlsx'; 
if exist(output_filename, 'file')
    delete(output_filename); 
end

% 遍历每个SAT值
for i = 1:length(sat_values)
    sat = sat_values(i);
    
    % 筛选数据
    sat_data = data( ...
        (data.SAT == sat) & ...
        ((data.AZ >= 300 | data.AZ <= 105) & ...
         (data.EL >= 5 & data.EL <= 25)), :);
    
    if isempty(sat_data)
        warning('SAT=%d 没有符合条件的数据，跳过。', sat);
        continue;
    end
    
    % --- 时间段划分逻辑 ---
    sat_sorted = sortrows(sat_data, 'TIME'); % 按TIME排序
    time_diffs = diff(sat_sorted.TIME);
    break_points = find(time_diffs > minutes(10));
    
    starts = [1; break_points + 1];
    ends = [break_points; height(sat_sorted)];
    
    % 初始化当前SAT的计数器
    up_count = 0;
    down_count = 0;
    
    % 遍历所有可能的时间段
    for j = 1:length(starts)
        seg_start = starts(j);
        seg_end = ends(j);
        segment = sat_sorted(seg_start:seg_end, :);
        
        % 判断段类型
        el_start = segment.EL(1);
        el_end = segment.EL(end);
        seg_type = '上升';
        if el_end < el_start
            seg_type = '下降';
        end
        
        % 检查同一天内的数量限制
        if strcmp(seg_type, '上升') && up_count >= 2 || ...
           strcmp(seg_type, '下降') && down_count >= 2
            continue; % 跳过超出限制的段
        end
        
        % 更新计数器
        if strcmp(seg_type, '上升')
            up_count = up_count + 1;
        else
            down_count = down_count + 1;
        end
        
        % --- 生成唯一工作表名称 ---
        sheet_name = sprintf('SAT%d_%s弧段%d', sat, seg_type, up_count + down_count);
        
        % 写入当前弧段数据（包含所有原始列：TIME, SAT, AZ, EL, SNR等）
        writetable(segment, output_filename, ...
            'Sheet', sheet_name, ...
            'WriteMode', 'append', ... % 追加模式
            'AutoFitWidth', false);
    end
end

% 保留原始数据（可选）
writetable(data, output_filename, 'Sheet', '原始数据备份', 'WriteMode', 'append');

% 打开文件
winopen(output_filename);