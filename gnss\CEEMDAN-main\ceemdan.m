%% CEEMDAN完整处理流程（嵌入式版本）
% 数据准备
data = readtable('g01.xlsx');    % 读取数据文件
SNR = data.SNR_dBHz;             % 提取SNR列数据

%% 参数设置
Nstd = 0.2;      % 噪声标准差系数 (0.1-0.5)
NR = 50;         % 噪声实现次数 (建议≥50)
MaxIter = 500;   % EMD最大迭代次数
SNRFlag = 1;     % 1=原始噪声，2=标准化噪声

%% ========== CEEMDAN核心算法 ==========
% 预处理
x = SNR(:);                      % 强制转换为列向量
desvio_x = std(x);
x_norm = x / desvio_x;

% 初始化变量
modes = zeros(length(x), 1);     % 列向量存储
aux = zeros(size(x_norm));
iter = zeros(NR, round(log2(length(x)) + 5));

% 生成白噪声库
white_noise = cell(NR, 1);
modes_white_noise = cell(NR, 1);
for i = 1:NR
    white_noise{i} = randn(size(x_norm)); % 列向量噪声
    modes_white_noise{i} = emd(white_noise{i}); % 分解噪声
end

% 第一模态提取
for i = 1:NR
    % 噪声处理
    noise = modes_white_noise{i}(:,1);
    noise_scaled = Nstd * noise / std(noise);
    
    % 加噪信号分解
    xi = x_norm + noise_scaled;
    try
        [imf, ~, it] = emd(xi, 'MaxNumIMF', 1, 'MaxNumExtrema', MaxIter);
        temp = imf(:,1);
    catch
        temp = xi;
        it = 0;
    end
    
    aux = aux + (xi - temp)/NR;
    iter(i,1) = it;
end
modes = x_norm - aux;   % 存储第一模态
medias = aux;
current_imf = 1;

% 后续模态提取
while true
    aux = zeros(size(x_norm));
    residual = medias(:,end);
    
    % 检查停止条件
    try
        test_imf = emd(residual, 'MaxNumIMF', 1);
        if size(test_imf,2) < 1
            break
        end
    catch
        break
    end
    
    % 模态提取循环
    for i = 1:NR
        % 噪声选择
        if size(modes_white_noise{i},2) > current_imf
            noise = modes_white_noise{i}(:,current_imf+1);
            if SNRFlag == 2
                noise = noise / std(noise);
            end
            noise_scaled = Nstd * noise;
            target = residual + std(residual)*noise_scaled;
        else
            target = residual;
        end
        
        % EMD分解
        try
            [imf, ~, it] = emd(target, 'MaxNumIMF', 1, 'MaxNumExtrema', MaxIter);
            if ~isempty(imf)
                temp = imf(:,end); % 取最后一个IMF
            else
                temp = target;
            end
        catch
            temp = target;
            it = 0;
        end
        
        aux = aux + temp/NR;
        iter(i,current_imf+1) = it;
    end
    
    % 更新模态
    modes = [modes, residual - aux]; % 水平拼接
    medias = [medias, aux];          % 存储中间结果
    current_imf = current_imf + 1;
end

% 添加最终残差
modes = [modes, medias(:,end)];

% 幅值恢复
modes = modes * desvio_x;

%% 结果可视化（优化版）
figure('Name','CEEMDAN分解结果','WindowState','maximized')
t = (0:length(x)-1)'/1000; % 假设采样率1kHz

% 绘制原始信号
subplot(size(modes,2)+1,1,1)
plot(t, x*desvio_x, 'b')
title('原始信号')
xlabel('时间 (s)'), ylabel('幅值')
xlim([t(1) t(end)])

% 绘制各IMF
for k = 1:size(modes,2)-1
    subplot(size(modes,2)+1,1,k+1)
    plot(t, modes(:,k))
    grid on
    title(['IMF ',num2str(k)])
    ylabel('幅值')
    xlim([t(1) t(end)])
end

% 绘制残差
subplot(size(modes,2)+1,1,size(modes,2)+1)
plot(t, modes(:,end))
 grid on
title('残差成分')
xlabel('时间 (s)'), ylabel('幅值')
xlim([t(1) t(end)])

%% 结果分析
% 计算各IMF能量占比
energy = var(modes);
energy_ratio = energy/sum(energy)*100;

% 显示能量分布
figure
pie(energy_ratio)
legend([arrayfun(@(n)sprintf('IMF%d (%.2f%%)',n,energy_ratio(n)),...
    1:size(modes,2))],'Location','eastoutside')
title('各IMF能量占比')

%% 数据导出
% 生成带时间戳的表格
outputTable = array2table([t, modes],...
    'VariableNames',['Time', arrayfun(@(x)sprintf('IMF%d',x),1:size(modes,2)-1),'Residual']);

% 保存为Excel文件
writetable(outputTable, 'CEEMDAN_Results.xlsx')

% 保存工作区变量
save('CEEMDAN_Workspace.mat')