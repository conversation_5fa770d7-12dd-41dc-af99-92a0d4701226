import os
import pandas as pd
import numpy as np
from datetime import timedelta

# 配置批量处理路径
input_folder = r'D:\ga-lstm\fenlishuju'          # 输入文件夹
output_folder = r'D:\ga-lstm\p351kechuli'        # 输出文件夹

if not os.path.exists(output_folder):
    os.makedirs(output_folder)

# 获取所有xlsx文件列表
file_list = [f for f in os.listdir(input_folder) if f.endswith('.xlsx')]

# 遍历每个文件
for file_idx, file_name in enumerate(file_list):
    filename = os.path.join(input_folder, file_name)
    output_filename = os.path.join(output_folder, file_name.replace('.xlsx', '_processed.xlsx'))

    # 如果输出文件已存在，则删除
    if os.path.exists(output_filename):
        os.remove(output_filename)

    # 读取Excel文件
    data = pd.read_excel(filename, sheet_name='Sheet1')

    # 将列名中的 'Time' 改为 'TIME'
    if 'Time' in data.columns:
        data.rename(columns={'Time': 'TIME'}, inplace=True)

    # 转换 TIME 列为 datetime 类型
    if not isinstance(data['TIME'].iloc[0], pd.Timestamp):
        data['TIME'] = pd.to_datetime(data['TIME'], format='%Y/%m/%d%H:%M:%S.%f', errors='coerce')

    # 获取唯一的 SAT 值
    sat_values = data['SAT'].unique()

    with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
        for sat in sat_values:
            # 筛选满足条件的数据
            sat_data = data[(data['SAT'] == sat) &
                            (data['AZ'] >= 0) & (data['AZ'] <= 360) &
                            (data['EL'] >= 5) & (data['EL'] <= 25)]

            if sat_data.empty:
                print(f"文件 {file_name} 中 SAT={sat} 没有符合条件的数据，跳过。")
                continue

            # 按时间排序
            sat_sorted = sat_data.sort_values(by='TIME')
            time_diffs = sat_sorted['TIME'].diff().dt.total_seconds() * 1e9  # 转换为纳秒
            break_points = np.where(time_diffs > 600e9)[0]  # 10分钟以内的间隔

            starts = [0] + list(break_points)
            ends = list(break_points - 1) + [len(sat_sorted) - 1]

            up_count = 0
            down_count = 0

            for j in range(len(starts)):
                seg_start = starts[j]
                seg_end = ends[j]
                segment = sat_sorted.iloc[seg_start:seg_end+1]

                el_start = segment['EL'].iloc[0]
                el_end = segment['EL'].iloc[-1]
                seg_type = '上升' if el_end >= el_start else '下降'

                # 控制最多保留两个上升段和两个下降段
                if seg_type == '上升' and up_count >= 2:
                    continue
                elif seg_type == '下降' and down_count >= 2:
                    continue

                if seg_type == '上升':
                    up_count += 1
                else:
                    down_count += 1

                sheet_name = f'SAT{sat}_{seg_type}弧段{up_count + down_count}'
                segment.to_excel(writer, sheet_name=sheet_name, index=False)

    print(f"文件 {file_name} 处理完成，结果保存在 {output_filename}")