import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import hdbscan
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import IsolationForest
import warnings
import matplotlib
import os
from openpyxl import load_workbook
from openpyxl.styles import PatternFill

warnings.filterwarnings("ignore", category=FutureWarning)
matplotlib.rcParams['font.family'] = ['SimHei', 'Microsoft YaHei', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示为方块的问题


# ===============================
# 1. 异常检测函数
# ===============================
def detect_outliers(data, min_cluster_size=50, min_samples=50):
    """两阶段异常值检测：HDBSCAN + Isolation Forest"""
    data = np.asarray(data).reshape(-1, 1)
    n_points = len(data)
    if n_points < 10:
        return np.zeros(n_points, dtype=bool), np.zeros(n_points), np.full(n_points, -1)

    # ---- 自适应参数 ----
    adjusted_min_cluster_size = min(min_cluster_size, max(5, n_points // 10))
    adjusted_min_samples = min(adjusted_min_cluster_size, max(2, n_points // 20))

    scaler = StandardScaler()
    data_scaled = scaler.fit_transform(data)

    try:
        clusterer = hdbscan.HDBSCAN(
            min_cluster_size=adjusted_min_cluster_size,
            min_samples=adjusted_min_samples,
            allow_single_cluster=False,
            prediction_data=True
        )
        clusterer.fit(data_scaled)
        hdbscan_scores = clusterer.outlier_scores_
        hdbscan_labels = clusterer.labels_
        if hdbscan_scores is None:
            hdbscan_scores = np.zeros(len(data))
    except Exception:
        hdbscan_scores = np.zeros(len(data))
        hdbscan_labels = np.full(len(data), -1)

    # ---- 第二阶段 Isolation Forest ----
    data_flat = data.flatten()
    small_value_mask = (data_flat < 10)

    if np.any(small_value_mask) and np.sum(small_value_mask) > 10:
        try:
            iso_forest = IsolationForest(
                contamination=min(0.2, 0.5),
                random_state=42
            )
            small_values = data[small_value_mask].reshape(-1, 1)
            iso_scores = iso_forest.fit_predict(small_values)

            combined_outlier = np.zeros(len(data_flat), dtype=bool)
            if np.any(~small_value_mask):
                hdbscan_threshold = np.percentile(
                    hdbscan_scores[~small_value_mask], 90)
                combined_outlier[~small_value_mask] = (
                    hdbscan_scores[~small_value_mask] > hdbscan_threshold)
            combined_outlier[small_value_mask] = (iso_scores == -1)
        except Exception:
            combined_outlier = hdbscan_scores > np.percentile(hdbscan_scores, 95)
    else:
        combined_outlier = hdbscan_scores > np.percentile(hdbscan_scores, 95) if np.any(hdbscan_scores) else np.zeros(len(data_flat), dtype=bool)

    return combined_outlier, hdbscan_scores, hdbscan_labels


# ===============================
# 2. 保存聚类图
# ===============================
def save_hdbscan_tree(data, outliers, labels, save_path, min_cluster_size=20, min_samples=5):
    scaler = StandardScaler()
    data_scaled = scaler.fit_transform(data.reshape(-1, 1))
    clusterer = hdbscan.HDBSCAN(
        min_cluster_size=min_cluster_size,
        min_samples=min_samples,
        allow_single_cluster=False,
        prediction_data=True
    ).fit(data_scaled)

    fig, axes = plt.subplots(1, 2, figsize=(14, 5))

    # ---- 聚类结果 ----
    normal_mask = ~outliers
    axes[0].scatter(
        np.arange(len(data))[normal_mask],
        data[normal_mask],
        c=labels[normal_mask],
        cmap="tab10",
        s=30,
        label="正常点"
    )
    axes[0].scatter(
        np.arange(len(data))[outliers],
        data[outliers],
        c="red",
        s=50,
        marker="x",
        label="异常点"
    )
    axes[0].set_title("HDBSCAN 聚类结果（红色为异常点）")
    axes[0].legend()

    # ---- Condensed tree ----
    plt.sca(axes[1])
    clusterer.condensed_tree_.plot(
        select_clusters=True,
        selection_palette=sns.color_palette("deep", 10)
    )
    axes[1].set_title("HDBSCAN Condensed Tree")

    plt.tight_layout()
    plt.savefig(save_path, dpi=150)
    plt.close(fig)


# ===============================
# 3. 主程序
# ===============================
if __name__ == "__main__":
    input_path = r"D:\ga-lstm\p351pinllv\p351_clustered4.xlsx"
    output_path = r"D:\ga-lstm\p351pinllv\p351_clustered5.xlsx"
    tree_dir = r"D:\ga-lstm\p351pinllv\tree"

    os.makedirs(tree_dir, exist_ok=True)

    df = pd.read_excel(input_path)
    df["Date"] = pd.to_datetime(df["StartTime"]).dt.date
    df["ClusterLabel"] = -1
    df["IsOutlier"] = False

    for date, group in df.groupby("Date"):
        print(f"\n=== {date} 聚类分析 ===")
        data = group["MainFrequency"].values
        if len(data) < 10:
            print(f"⚠️ {date} 数据点不足 {len(data)}，跳过")
            continue

        outliers, scores, labels = detect_outliers(data, min_cluster_size=20, min_samples=10)
        print(f"检测到的异常点数量: {np.sum(outliers)}")

        df.loc[group.index, "ClusterLabel"] = labels
        df.loc[group.index, "IsOutlier"] = outliers

        save_path = os.path.join(tree_dir, f"{date}.png")
        save_hdbscan_tree(data, outliers, labels, save_path, min_cluster_size=20, min_samples=10)

    # 先保存结果
    df.to_excel(output_path, index=False)

    # 用 openpyxl 加载并给异常点上红底
    wb = load_workbook(output_path)
    ws = wb.active
    red_fill = PatternFill(start_color="FF9999", end_color="FF9999", fill_type="solid")

    isoutlier_col = list(ws[1]).index([c for c in ws[1] if c.value == "IsOutlier"][0]) + 1
    for row in ws.iter_rows(min_row=2, values_only=False):
        if row[isoutlier_col - 1].value:
            for cell in row:
                cell.fill = red_fill

    wb.save(output_path)
    print(f"\n✅ 处理完成，结果已保存到 {output_path}")
    print(f"✅ 聚类图已保存到 {tree_dir}")
