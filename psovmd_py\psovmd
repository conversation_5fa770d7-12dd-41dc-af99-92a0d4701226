import numpy as np
import pandas as pd
import os
from datetime import datetime
import matplotlib.pyplot as plt
from scipy.signal import lombscargle
from scipy.optimize import curve_fit
from scipy.fftpack import hilbert
from vmdpy import VMD
from scipy.io import savemat


# =================== 工具函数 ===================
def preprocess_data(time_col, el_col, snr_col):
    """预处理：时间解析、无效值过滤、sin(EL) 计算、SNR 转换"""
    try:
        time_col = pd.to_datetime(time_col)
    except:
        raise ValueError("时间列格式无法解析")

    valid_idx = (~pd.isna(time_col)) & (~pd.isna(el_col)) & (~pd.isna(snr_col))
    if sum(valid_idx) < 2:
        raise ValueError("有效数据不足（至少需要2个点）")

    sin_el = np.sin(np.radians(el_col[valid_idx]))
    snr_volts = 10 ** (snr_col[valid_idx] / 20)
    time_valid = time_col[valid_idx]

    return sin_el, snr_volts, time_valid


def make_strictly_increasing(x, min_delta=1e-9):
    x = np.array(x).copy()
    for i in range(1, len(x)):
        if x[i] <= x[i - 1]:
            required_delta = x[i - 1] - x[i] + min_delta
            x[i] += required_delta
    sort_idx = np.argsort(x)
    return x[sort_idx], sort_idx


def save_spectrum_plot(sheet_name, sinEL_sorted, signal, output_folder, A=None, phi=None, offset=None):
    f_custom = np.arange(0, 400, 0.1)
    P = lombscargle(sinEL_sorted, signal, f_custom, normalize=True)

    plt.figure(figsize=(10, 6))
    plt.subplot(2, 1, 1)
    plt.plot(sinEL_sorted, signal, "red")
    plt.title(f"{sheet_name} 残差分布")
    plt.xlabel("sin(EL)")
    plt.ylabel("Signal")

    plt.subplot(2, 1, 2)
    plt.plot(f_custom, P)
    plt.title(f"{sheet_name} 频谱")
    plt.xlabel("Frequency (Hz)")
    plt.ylabel("Power")
    plt.grid(True)
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder, f"{sheet_name}_频谱.png"))
    plt.close()

    if A is not None and phi is not None and offset is not None:
        fit_signal = A * np.sin(2 * np.pi * sinEL_sorted + phi) + offset
        plt.figure(figsize=(10, 4))
        plt.plot(sinEL_sorted, signal, label='Original')
        plt.plot(sinEL_sorted, fit_signal, 'r--', label=f'Fit: A={A:.4f}, φ={phi:.4f}')
        plt.legend()
        plt.title(f"{sheet_name} 拟合结果")
        plt.xlabel("sin(EL)")
        plt.ylabel("Signal")
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(os.path.join(output_folder, f"{sheet_name}_拟合.png"))
        plt.close()


def save_vmd_decomposition_plot(sheet_name, sinEL_sorted, snr_volts_sorted, u, output_folder, selected_imfs_indices=None):
    num_imfs = u.shape[0]
    plt.figure(figsize=(10, 1.5 * (num_imfs + 1)))

    # 原始信号
    plt.subplot(num_imfs + 1, 1, 1)
    plt.plot(sinEL_sorted, snr_volts_sorted, "red")
    plt.title(f"{sheet_name} - 原始数据")
    plt.ylabel("幅值")

    # 所有 IMF
    for i in range(num_imfs):
        plt.subplot(num_imfs + 1, 1, i + 2)
        if selected_imfs_indices is not None and i in selected_imfs_indices:
            plt.plot(sinEL_sorted, u[i, :], 'b-', linewidth=1.5, label='Selected')
        else:
            plt.plot(sinEL_sorted, u[i, :], 'gray', alpha=0.6, linewidth=1)

        plt.title(f"IMF {i + 1}")
        plt.ylabel("幅值")
        plt.grid(True)

    plt.tight_layout()
    plt.savefig(os.path.join(output_folder, f"{sheet_name}_VMD分解.png"))
    plt.close()


def export_summary(results, folder):
    sheet_names = [r['SheetName'] for r in results]
    main_freqs = [r['MainFreq'] for r in results]
    amplitudes = [r.get('Amplitude') for r in results]
    phases = [r.get('Phase') for r in results]
    offsets = [r.get('Offset') for r in results]
    start_times = [r['StartTime'] for r in results]
    end_times = [r['EndTime'] for r in results]
    optimal_ks = [r['OptimalK'] for r in results]
    optimal_alphas = [r['OptimalAlpha'] for r in results]

    df = pd.DataFrame({
        'Sheet': sheet_names,
        'MainFrequency': main_freqs,
        'Amplitude': amplitudes,
        'Phase': phases,
        'Offset': offsets,
        'StartTime': start_times,
        'EndTime': end_times,
        'OptimalK': optimal_ks,
        'OptimalAlpha': optimal_alphas
    })
    df.to_csv(os.path.join(output_folder, '频率汇总表.csv'), index=False)
    savemat(os.path.join(folder, '完整结果.mat'), {'results': results})


def log_error(folder, sheet, message):
    os.makedirs(folder, exist_ok=True)
    with open(os.path.join(folder, 'error_log.txt'), 'a') as f:
        f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M')}]{sheet}: {message}\n")


# =================== 目标函数 ===================
def envelope_entropy(signal):
    env = np.abs(hilbert(signal))
    norm_env = env / (np.sum(env) + 1e-10)
    entropy = -np.sum(norm_env * np.log2(norm_env + 1e-10))
    return entropy


def calculate_energy_loss(original_signal, imf):
    total_energy_imf = np.sum(imf ** 2)
    total_energy_original = np.sum(original_signal ** 2)
    return abs(total_energy_original - total_energy_imf)


def objective_function(x, sinEL, snr_volts, min_delta, weight_envelope_entropy, weight_energy_loss):
    num_imfs = int(round(x[0]))
    alpha = x[1]

    try:
        signal = snr_volts.copy()
        u, _, _ = VMD(signal, alpha, 0, num_imfs, 1e-7, 1 / len(signal), 1e-9)

        # ⚠️ 确保长度一致后再排序
        min_len = min(len(signal), u.shape[1])
        signal = signal[:min_len]
        u = u[:, :min_len]

        sinEL_sorted, sort_idx = make_strictly_increasing(sinEL[:min_len], min_delta)
        residuals = signal - u[-1, :]

        residuals = residuals[sort_idx]
        u = u[:, sort_idx]

        entropy_value = envelope_entropy(residuals)
        loss_value = calculate_energy_loss(signal, u)

        fitness = weight_envelope_entropy * entropy_value + weight_energy_loss * loss_value
        return fitness
    except Exception as e:
        print(f"目标函数异常: {str(e)}")
        return float('inf')


# =================== PSO 优化器 ===================
def pso_optimization(obj_func, pop_size, max_iter, dim, lb, ub):
    X = np.random.rand(pop_size, dim)
    for i in range(dim):
        X[:, i] = lb[i] + X[:, i] * (ub[i] - lb[i])
    X[:, 0] = np.round(X[:, 0])

    V = np.random.uniform(-1, 1, (pop_size, dim))

    personal_best_X = np.copy(X)
    value = np.array([obj_func(x) for x in X])
    personal_best_value = np.copy(value)

    global_best_X = X[np.argmin(value)]
    global_best_value = min(value)

    w = 0.729
    c1 = 1.494
    c2 = 1.494

    for iter in range(max_iter):
        for i in range(pop_size):
            r1 = np.random.rand()
            r2 = np.random.rand()

            V[i] = w * V[i] + c1 * r1 * (personal_best_X[i] - X[i]) + c2 * r2 * (global_best_X - X[i])
            X[i] += V[i]

            X[i, 0] = round(np.clip(X[i, 0], lb[0], ub[0]))
            X[i, 1] = np.clip(X[i, 1], lb[1], ub[1])

            fit = obj_func(X[i])
            if fit < personal_best_value[i]:
                personal_best_value[i] = fit
                personal_best_X[i] = X[i].copy()

            if fit < global_best_value:
                global_best_value = fit
                global_best_X = X[i].copy()

        print(f"Iteration {iter+1}/{max_iter} | Best Fitness: {global_best_value:.6f}")

    return global_best_X, global_best_value


# =================== 正弦拟合函数 ===================
def sinusoidal_model(x, A, phi, offset):
    return A * np.sin(2 * np.pi * x + phi) + offset


def fit_sinusoid(x, y):
    try:
        A0 = np.std(y) * 2
        phi0 = 0
        offset0 = np.mean(y)

        params, _ = curve_fit(lambda t, a, p, o: sinusoidal_model(t, a, p, o), x, y,
                              p0=[A0, phi0, offset0], maxfev=5000)
        A, phi, offset = params
        return A, phi, offset
    except Exception as e:
        print(f"拟合失败: {e}")
        return None, None, None


# =================== 主程序入口 ===================
if __name__ == "__main__":
    filename = 'g01.xls'
    output_folder = 'NoTime_Analysis'
    min_delta = 1e-9

    # PSO 参数
    pop_size = 20
    max_iter = 15
    dim = 2
    lb = [3, 1000]
    ub = [8, 5000]
    weight_envelope_entropy = 0.5
    weight_energy_loss = 0.5
    os.makedirs(output_folder, exist_ok=True)

    # 读取 Excel 表格列表
    xls = pd.ExcelFile(filename)
    sheet_list = xls.sheet_names

    results = []

    for sheet in sheet_list:
        print(f"\n开始处理工作表: {sheet}")

        try:
            # 读取数据
            df = pd.read_excel(filename, sheet_name=sheet)
            # 预处理
            sinEL, snr_volts, time_valid = preprocess_data(df['TIME'], df['EL'], df['SNR'])
            start_time = min(time_valid)
            end_time = max(time_valid)

            print(f"原始数据长度: {len(sinEL)}")

            # PSO 优化
            best_solution, best_fitness = pso_optimization(
                lambda x: objective_function(x, sinEL, snr_volts, min_delta,
                                            weight_envelope_entropy, weight_energy_loss),
                pop_size, max_iter, dim, lb, ub
            )

            vmd_num_imfs = int(round(best_solution[0]))
            vmd_alpha = best_solution[1]

            print(f"最优 K 值: {vmd_num_imfs}, 最优 alpha 值: {vmd_alpha:.2f}")

            # 核心分析
            signal = snr_volts.copy()
            u, _, _ = VMD(signal, vmd_alpha, 0, vmd_num_imfs, 1e-7, 1 / len(signal), 1e-9)

            # ⚠️ 长度一致性检查
            if u.shape[1] != len(signal):
                print(f"⚠️ VMD 返回的 IMF 长度与输入不一致: {u.shape[1]} vs {len(signal)}")
                min_len = min(len(signal), u.shape[1])
                sinEL = sinEL[:min_len]
                signal = signal[:min_len]
                u = u[:, :min_len]

            # 统一排序
            sinEL_sorted, sort_idx = make_strictly_increasing(sinEL, min_delta)
            snr_volts_sorted = signal[sort_idx]
            u_sorted = u[:, sort_idx]

            # 筛选相关性强的 IMF 分量
            selected_imfs = []
            selected_imf_indices = []
            for i in range(u_sorted.shape[0]):
                if i == 0:
                    continue
                corr = np.corrcoef(u_sorted[i, :], snr_volts_sorted)[0, 1]
                if corr > 0.3:
                    selected_imfs.append(u_sorted[i, :])
                    selected_imf_indices.append(i)

            if not selected_imfs:
                print("没有找到符合条件的 IMF 分量")
                continue

            # 合成信号
            reconstructed_signal = np.sum(selected_imfs, axis=0)

            # Lomb-Scargle 分析
            f_custom = np.arange(0, 400, 0.1)
            P = lombscargle(sinEL_sorted, reconstructed_signal, f_custom, normalize=True)
            main_freq = f_custom[np.argmax(P)]

            # 拟合正弦波
            A, phi, offset = fit_sinusoid(sinEL_sorted, reconstructed_signal)

            # 存储结果
            results.append({
                'SheetName': sheet,
                'MainFreq': main_freq,
                'Amplitude': A,
                'Phase': phi,
                'Offset': offset,
                'Residuals': np.column_stack((sinEL_sorted, reconstructed_signal)),
                'StartTime': start_time,
                'EndTime': end_time,
                'OptimalK': vmd_num_imfs,
                'OptimalAlpha': vmd_alpha,
                'SelectedIMFs': selected_imfs
            })

            # 可视化
            save_spectrum_plot(sheet, sinEL_sorted, reconstructed_signal, output_folder, A, phi, offset)
            save_vmd_decomposition_plot(sheet, sinEL_sorted, snr_volts_sorted, u_sorted, output_folder, selected_imf_indices)

        except Exception as e:
            print(f"分析失败: {str(e)}")
            log_error(output_folder, sheet, str(e))
            continue

    # 导出结果
    export_summary(results, output_folder)

    print("\n==== 分析完成 ====")