% 清除潜在变量冲突
clear
colormap jet

% 读取数据
filename = 'ssa-vmd.xlsx';
data = readtable(filename);

% 提取列数据
y = data{:, 2};
x = data{:, 3};

% 线性回归拟合
p = polyfit(x, y, 1);
y_pred = polyval(p, x);

% 计算拟合曲线的斜率
slope = p(1);

% 计算两组数据的相关系数
corr_coeff = corr(x, y);

% 计算距离
distances = abs(x - y);

% 绘制散点图
figure;
scatter(x, y, 20, distances, 'filled');
% ylim([0,3])
% xlim([0,6])
% 反转颜色映射，让距离近的为红色，远的为蓝色
cmap = flip(jet(256));
colormap(cmap);

% 正确设置 colorbar 标签（兼容所有版本）
cb = colorbar;
% ylabel(cb, 'Distance (Closer to Red, Farther to Blue)'); 

% 添加拟合线和 1:1 线
hold on;
h1 = plot(x, y_pred, 'm-', 'LineWidth', 1.5, 'DisplayName', ['斜率: ', num2str(slope)]);
h2 = plot([min(x), max(x)], [min(x), max(x)], 'k--', 'DisplayName', '1:1 Line');

% 创建一个不可见的绘图对象用于添加相关系数信息
h3 = plot(nan, nan, 'DisplayName', ['相关系数R: ', num2str(corr_coeff)]);

% 在 X 轴上方显示 SSA - VMD
x_center = mean([min(x), max(x)]); % 计算 X 轴范围的中心位置
y_min = 0;
text(x_center, y_min, 'SSA-VMD', 'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom','FontSize',12);

hold off;

% 添加标签和图例
xlabel('反演值(m)');
ylabel('实测值(m)');
% title('Scatter Plot with OLS Fit and 1:1 Line');
hLegend = legend([h1, h2, h3], 'Location', 'best');

% 设置图例文字大小
set(hLegend, 'FontSize', 12); 

% 调整图例整体大小和位置（这里示例性地调整位置和大小，你可按需修改）
newPosition = get(hLegend, 'Position');
newPosition(3) = newPosition(3) * 1; % 宽度变为原来的 1.2 倍
newPosition(4) = newPosition(4) * 1; % 高度变为原来的 1.2 倍
set(hLegend, 'Position', newPosition);