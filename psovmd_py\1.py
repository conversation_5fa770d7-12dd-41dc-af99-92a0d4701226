import pandas as pd

# 1. 读取 xlsx 文件（替换 "your_file.xlsx" 为实际文件路径）
df = pd.read_excel(r"C:\Users\<USER>\Desktop\GLSI1_snow_cover_plot_1.xlsx")

# 2. 处理日期：提取日期部分（原日期含时间，拆分后取日期）
df["日期"] = pd.to_datetime(df["Date"]).dt.date  

# 3. 按日期分组，计算雪深均值
result = df.groupby("日期")["(Observed) Snow Depth (cm)"].mean().reset_index()

# 4. 保存结果到新 xlsx 文件（可自定义输出路径）
result.to_excel("snow_depth_mean.xlsx", index=False)  