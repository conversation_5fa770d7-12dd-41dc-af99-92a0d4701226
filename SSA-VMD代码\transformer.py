
import pandas as pd
import torch
import torch.nn as nn
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from torch.utils.data import TensorDataset, DataLoader
import matplotlib.pyplot as plt

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

# 设置 matplotlib 中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

# ==========================
# 读取数据
# ==========================
df = pd.read_excel(r'D:\ga-lstm\p351pinllv\p351_clustered5.xlsx', sheet_name='Sheet1')
df = df.reset_index(drop=False)  # 保留原始索引
original_indices = df['index'].values

# 新增代码：检查数据中是否存在 NaN 或无穷大值
if df.isnull().values.any():
    print("警告: 数据中存在缺失值 (NaN)")
    print("缺失值统计:")
    print(df.isnull().sum())

    # 删除包含NaN的行
    df = df.dropna()
    print(f"删除缺失值后，剩余数据行数: {len(df)}")

# 只检查数值列的无穷大值
numeric_columns = df.select_dtypes(include=[np.number])
if len(numeric_columns.columns) > 0 and np.isinf(numeric_columns.values).any():
    print("警告: 数据中存在无穷大值")
    # 替换无穷大值为NaN，然后删除
    df = df.replace([np.inf, -np.inf], np.nan).dropna()
    print(f"删除无穷大值后，剩余数据行数: {len(df)}")

# 重新获取处理后的数据
original_indices = df['index'].values
X = df[['MainFrequency', 'AZ_Mean', 'Residual_Amplitude', 'Residual_Phase']].values
y = df['H'].values

print(f"最终数据形状: X={X.shape}, y={y.shape}")

# ==========================
# 数据划分
# ==========================
X_train, X_test, y_train, y_test, idx_train, idx_test = train_test_split(
    X, y, original_indices, test_size=0.2, random_state=42
)

# ==========================
# 特征标准化
# ==========================
scaler_X = StandardScaler()
X_train = scaler_X.fit_transform(X_train)
X_test = scaler_X.transform(X_test)

# y归一化
y_mean = y_train.mean()
y_std = y_train.std()
y_train_scaled = (y_train - y_mean) / y_std
y_test_scaled = (y_test - y_mean) / y_std

# 转换为张量
X_train_tensor = torch.tensor(X_train, dtype=torch.float32)
y_train_tensor = torch.tensor(y_train_scaled, dtype=torch.float32).unsqueeze(1)
X_test_tensor = torch.tensor(X_test, dtype=torch.float32)
y_test_tensor = torch.tensor(y_test_scaled, dtype=torch.float32).unsqueeze(1)

train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)

test_dataset = TensorDataset(X_test_tensor, y_test_tensor)
test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)

# ==========================
# 定义 Transformer 回归模型
# ==========================
class TransformerRegressor(nn.Module):
    def __init__(self, input_size, d_model=32, nhead=2, num_layers=1, dropout=0.3):
        super().__init__()
        self.embedding = nn.Sequential(
            nn.Linear(input_size, d_model),
            nn.LayerNorm(d_model),
            nn.Dropout(dropout)
        )
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model, nhead=nhead, dropout=dropout, batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        self.fc = nn.Sequential(
            nn.Linear(d_model, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, 1)
        )
        
    def forward(self, x):
        x = x.unsqueeze(1)  # [batch, seq_len=1, input_size]
        x = self.embedding(x)
        x = self.transformer_encoder(x)
        x = x.squeeze(1)
        x = self.fc(x)
        return x

model = TransformerRegressor(input_size=4)
criterion = nn.MSELoss()
optimizer = torch.optim.Adam(model.parameters(), lr=1e-4, weight_decay=1e-5)
scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=10, min_lr=1e-6)

# ==========================
# 训练循环（带梯度裁剪）
# ==========================
num_epochs = 10
train_losses, val_losses = [], []

for epoch in range(num_epochs):
    model.train()
    running_loss = 0
    for batch_X, batch_y in train_loader:
        optimizer.zero_grad()
        outputs = model(batch_X)
        loss = criterion(outputs, batch_y)
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)  # 梯度裁剪
        optimizer.step()
        running_loss += loss.item()
    epoch_train_loss = running_loss / len(train_loader)
    train_losses.append(epoch_train_loss)

    # 验证
    model.eval()
    val_loss = 0
    with torch.no_grad():
        for batch_X, batch_y in test_loader:
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            val_loss += loss.item()
    epoch_val_loss = val_loss / len(test_loader)
    val_losses.append(epoch_val_loss)

    print(f'Epoch {epoch+1}/{num_epochs}, 训练损失: {epoch_train_loss:.6f}, 验证损失: {epoch_val_loss:.6f}')
    scheduler.step(epoch_val_loss)

# ==========================
# 预测并反归一化
# ==========================
model.eval()
with torch.no_grad():
    train_pred_scaled = model(X_train_tensor).squeeze().numpy()
    test_pred_scaled = model(X_test_tensor).squeeze().numpy()
    
train_predictions = train_pred_scaled * y_std + y_mean
test_predictions = test_pred_scaled * y_std + y_mean

# ==========================
# 保存结果
# ==========================
train_results = pd.DataFrame({
    'original_index': idx_train,
    'actual': y_train,
    'predicted': train_predictions
}).sort_values('original_index')
train_results.to_excel('train_results.xlsx', index=False)

test_results = pd.DataFrame({
    'original_index': idx_test,
    'actual': y_test,
    'predicted': test_predictions
}).sort_values('original_index')
test_results.to_excel('test_results.xlsx', index=False)

# ==========================
# 评估指标
# ==========================
def calculate_metrics(y_true, y_pred):
    rmse = np.sqrt(np.mean((y_true - y_pred) ** 2))
    mae = np.mean(np.abs(y_true - y_pred))
    return rmse, mae

rmse_train, mae_train = calculate_metrics(y_train, train_predictions)
rmse_test, mae_test = calculate_metrics(y_test, test_predictions)
print(f'Train RMSE: {rmse_train:.6f}, Train MAE: {mae_train:.6f}')
print(f'Test RMSE: {rmse_test:.6f}, Test MAE: {mae_test:.6f}')

# ==========================
# 绘制预测值与实际值对比图
# ==========================
plt.figure(figsize=(15, 10))

# 训练集对比图
plt.subplot(2, 2, 1)
plt.scatter(y_train, train_predictions, alpha=0.5, s=1)
plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--', lw=2)
plt.xlabel('实际值')
plt.ylabel('预测值')
plt.title(f'训练集预测对比\nRMSE: {rmse_train:.3f}, MAE: {mae_train:.3f}')
plt.grid(True, alpha=0.3)

# 测试集对比图
plt.subplot(2, 2, 2)
plt.scatter(y_test, test_predictions, alpha=0.5, s=1)
plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
plt.xlabel('实际值')
plt.ylabel('预测值')
plt.title(f'测试集预测对比\nRMSE: {rmse_test:.3f}, MAE: {mae_test:.3f}')
plt.grid(True, alpha=0.3)

# 按原始顺序排列的时间序列对比
plt.subplot(2, 2, 3)
# 合并训练集和测试集结果，按原始索引排序
all_results = pd.DataFrame({
    'original_index': np.concatenate([idx_train, idx_test]),
    'actual': np.concatenate([y_train, y_test]),
    'predicted': np.concatenate([train_predictions, test_predictions]),
    'dataset': ['train'] * len(y_train) + ['test'] * len(y_test)
}).sort_values('original_index')

# 绘制按原始顺序的时间序列（取前2000个点）
n_show_all = min(2000, len(all_results))
show_data = all_results.iloc[:n_show_all]
plt.plot(show_data['original_index'], show_data['actual'], label='实际值', alpha=0.7, linewidth=1)
plt.plot(show_data['original_index'], show_data['predicted'], label='预测值', alpha=0.7, linewidth=1)
plt.xlabel('原始索引')
plt.ylabel('H值')
plt.title(f'按原始顺序的时间序列对比（前{n_show_all}个点）')
plt.legend()
plt.grid(True, alpha=0.3)

# 训练集和测试集分别显示（按原始顺序）
plt.subplot(2, 2, 4)
train_data = all_results[all_results['dataset'] == 'train'].iloc[:1000]
test_data = all_results[all_results['dataset'] == 'test'].iloc[:1000]

plt.plot(train_data['original_index'], train_data['actual'], 'b-', label='训练集实际值', alpha=0.7, linewidth=1)
plt.plot(train_data['original_index'], train_data['predicted'], 'b--', label='训练集预测值', alpha=0.7, linewidth=1)
plt.plot(test_data['original_index'], test_data['actual'], 'r-', label='测试集实际值', alpha=0.7, linewidth=1)
plt.plot(test_data['original_index'], test_data['predicted'], 'r--', label='测试集预测值', alpha=0.7, linewidth=1)
plt.xlabel('原始索引')
plt.ylabel('H值')
plt.title('训练集与测试集对比（按原始顺序）')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('transformer_prediction_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

# ==========================
# 绘制完整的按原始顺序的时间序列图
# ==========================
plt.figure(figsize=(15, 8))

# 上图：完整的时间序列对比
plt.subplot(2, 1, 1)
plt.plot(all_results['original_index'], all_results['actual'], label='实际值', alpha=0.8, linewidth=0.8, color='blue')
plt.plot(all_results['original_index'], all_results['predicted'], label='预测值', alpha=0.8, linewidth=0.8, color='red')
plt.xlabel('原始索引')
plt.ylabel('H值')
plt.title('完整数据集按原始顺序的预测对比')
plt.legend()
plt.grid(True, alpha=0.3)

# 下图：区分训练集和测试集
plt.subplot(2, 1, 2)
train_mask = all_results['dataset'] == 'train'
test_mask = all_results['dataset'] == 'test'

plt.scatter(all_results[train_mask]['original_index'], all_results[train_mask]['actual'],
           c='lightblue', s=0.5, alpha=0.6, label='训练集实际值')
plt.scatter(all_results[train_mask]['original_index'], all_results[train_mask]['predicted'],
           c='blue', s=0.5, alpha=0.6, label='训练集预测值')
plt.scatter(all_results[test_mask]['original_index'], all_results[test_mask]['actual'],
           c='lightcoral', s=0.5, alpha=0.6, label='测试集实际值')
plt.scatter(all_results[test_mask]['original_index'], all_results[test_mask]['predicted'],
           c='red', s=0.5, alpha=0.6, label='测试集预测值')

plt.xlabel('原始索引')
plt.ylabel('H值')
plt.title('训练集与测试集分布（按原始顺序）')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('transformer_original_order_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

# ==========================
# 绘制残差分析图
# ==========================
plt.figure(figsize=(12, 8))

# 训练集残差
train_residuals = y_train - train_predictions
plt.subplot(2, 2, 1)
plt.scatter(train_predictions, train_residuals, alpha=0.5, s=1)
plt.axhline(y=0, color='r', linestyle='--')
plt.xlabel('预测值')
plt.ylabel('残差')
plt.title('训练集残差分析')
plt.grid(True, alpha=0.3)

# 测试集残差
test_residuals = y_test - test_predictions
plt.subplot(2, 2, 2)
plt.scatter(test_predictions, test_residuals, alpha=0.5, s=1)
plt.axhline(y=0, color='r', linestyle='--')
plt.xlabel('预测值')
plt.ylabel('残差')
plt.title('测试集残差分析')
plt.grid(True, alpha=0.3)

# 训练集残差直方图
plt.subplot(2, 2, 3)
plt.hist(train_residuals, bins=50, alpha=0.7, edgecolor='black')
plt.xlabel('残差')
plt.ylabel('频次')
plt.title('训练集残差分布')
plt.grid(True, alpha=0.3)

# 测试集残差直方图
plt.subplot(2, 2, 4)
plt.hist(test_residuals, bins=50, alpha=0.7, edgecolor='black')
plt.xlabel('残差')
plt.ylabel('频次')
plt.title('测试集残差分布')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('transformer_residual_analysis.png', dpi=300, bbox_inches='tight')
plt.show()

print("图表已保存为 'transformer_prediction_comparison.png' 和 'transformer_residual_analysis.png'")
