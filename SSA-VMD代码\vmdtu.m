clc; clear; close all;
warning('off', 'MATLAB:table:ModifiedVarnames');

%% 参数配置 
filename = 'p3510050_processed.xlsx';           % 输入文件路径 
vmd_num_imfs = 5;                      % VMD模态数
vmd_alpha = 2000;                      % VMD惩罚因子
output_folder = 'NoTime_Analysis';     % 结果输出目录 
min_delta = 1e-9;                     % 严格递增处理参数 

%% 初始化环境 
if ~exist(output_folder, 'dir')
    mkdir(output_folder);
end 
[~, sheetList] = xlsfinfo(filename);
results = struct('SheetName', {}, 'MainFreq', {}, 'Residuals', {}, 'StartTime', {}, 'EndTime', {});

%% 主处理循环 
for sheetIdx = 1:length(sheetList)
    currentSheet = sheetList{sheetIdx};
    try 
        fprintf('\n处理进度: %d/%d | 当前工作表: %s\n', sheetIdx, length(sheetList), currentSheet);
        
        % 数据读取 (包含时间列)
        opts = detectImportOptions(filename, 'Sheet', currentSheet);
        opts.SelectedVariableNames = {'TIME', 'EL_deg', 'SNR_dBHz'};  
        rawData = readtable(filename, opts);
        
        % 数据预处理 
        [sinEL, snr_volts, time_valid] = preprocessData(rawData.TIME, rawData.EL_deg, rawData.SNR_dBHz);
        start_time = min(time_valid);
        end_time = max(time_valid);
        
        % 核心分析 (接收VMD参数)
        [zhupin, residuals, sinEL_sorted, imf] = coreAnalysis(sinEL, snr_volts, vmd_num_imfs, vmd_alpha, min_delta);
        
        % 结果存储 
        results(sheetIdx).SheetName = currentSheet;
        results(sheetIdx).MainFreq = zhupin;
        if ~isempty(sinEL_sorted) && ~isempty(residuals)
            results(sheetIdx).Residuals = [sinEL_sorted(:), residuals(:)];
        else
            results(sheetIdx).Residuals = [];
        end
        results(sheetIdx).StartTime = start_time;
        results(sheetIdx).EndTime = end_time;
        
        % 生成图表 
        if ~isempty(sinEL_sorted) && ~isempty(residuals)
            generateSpectrumPlot(currentSheet, sinEL_sorted, residuals, imf);
        end
        
    catch ME 
        logError(output_folder, currentSheet, ME.message); 
        continue
    end 
end 

%% 结果导出 
    exportWorkbookSummary(results, output_folder, workbook_name);
    fprintf('  工作簿处理完成: %s\n', workbook_name);
%% 预处理函数 
function [sinEL, snr_volts, time_valid] = preprocessData(Time, EL, SNR)
    % 转换时间格式并过滤无效数据
    if ~isdatetime(Time)
        try
            Time = datetime(Time, 'ConvertFrom', 'excel');
        catch
            error('时间列格式无法解析');
        end
    end
    validIdx = ~isnat(Time) & ~isnan(EL) & ~isnan(SNR);
    if sum(validIdx) < 2
        error('有效数据不足（至少需要2个点）');
    end
    sinEL = sind(EL(validIdx));
    snr_volts = 10.^(SNR(validIdx)/20);
    time_valid = Time(validIdx);
end

%% 核心分析函数（使用VMD分解）
function [zhupin, residuals, sinEL_sorted, imf] = coreAnalysis(sinEL, snr_volts, num_imfs, alpha, min_delta)
    zhupin = NaN;
    residuals = [];
    sinEL_sorted = [];
    imf = [];
    try
        if isempty(sinEL) || isempty(snr_volts) || length(sinEL) ~= length(snr_volts)
            error('输入数据不合法');
        end
        
        % VMD分解
        [imf, ~] = vmd(snr_volts(:), 'NumIMFs', num_imfs, 'PenaltyFactor', alpha);
        % 修改residuals的计算方式
        residuals =  sum(imf(:, 1:4),2); 
        
        % 严格递增处理
        [sinEL_sorted, sortIdx] = sort(sinEL);
        sinEL_sorted = makeStrictlyIncreasing(sinEL_sorted, min_delta);
        residuals = residuals(sortIdx); % 同步排序残差
        imf = imf(sortIdx, :); % 同步排序IMF
        
        % Lomb-Scargle分析
        f_custom = 0:0.1:400;
        [P, f] = plomb(residuals, sinEL_sorted, f_custom);
        
        % 提取主频
        [~, maxIdx] = max(P);
        zhupin = f(maxIdx);
        
    catch ME
        error('分析失败: %s', ME.message);
    end
end

%% 可视化函数 
function generateSpectrumPlot(sheetName, sinEL_sorted, residuals, imf)
  num_imfs = size(imf, 2);
fig = figure;
% 显示原始序列
ax = subplot(num_imfs + 1, 1, 1);
plot(sinEL_sorted, residuals + imf(:, end));
title_text = sprintf('原始SNR序列');
% 将标题放在子图内部右下角
text(0.98, 0.02, title_text, 'HorizontalAlignment', 'right', 'VerticalAlignment', 'bottom', 'Parent', ax, 'Units', 'normalized');
set(ax, 'XTickLabel', []); % 不显示原始序列子图的横坐标刻度值
xlim(ax, [0.08, 0.43]); % 设置横坐标范围

% 显示VMD分解后的分量
for i = 1:num_imfs
    ax = subplot(num_imfs + 1, 1, i + 1);
    plot(sinEL_sorted, imf(:, i),'c');
    title_text = sprintf('IMF %d', i);
    % 将标题放在子图内部右下角
    text(0.98, 0.02, title_text, 'HorizontalAlignment', 'right', 'VerticalAlignment', 'bottom', 'Parent', ax, 'Units', 'normalized');
    
    if i < num_imfs
        set(ax, 'XTickLabel', []); % 非最后一个子图不显示横坐标刻度值
    end
    xlim(ax, [0.08, 0.43]); % 设置横坐标范围
end
  

    % 显示频谱
    f_custom = 0:0.1:400;
    [P, f] = plomb(residuals, sinEL_sorted,f_custom);
    figure;
    xlim([0 400]);
    plot(f, P);
    xlabel('Frequency (Hz)');
    ylabel('Power');
    grid on;
end
%% 辅助函数 
function logError(folder, sheet, msg)
    fid = fopen(fullfile(folder, 'error_log.txt'),  'a');
    fprintf(fid, '[%s] %s: %s\n', datestr(now, 'yyyy-mm-dd HH:MM'), sheet, msg);
    fclose(fid);
end 

%% 导出汇总表函数
function exportSummary(results, folder)
    sheetNames = {results.SheetName}';
    mainFreqs = [results.MainFreq]';
    startTimes = {results.StartTime}';
    endTimes = {results.EndTime}';
    T = table(sheetNames, mainFreqs, startTimes, endTimes, ...
        'VariableNames', {'Sheet', 'MainFrequency', 'StartTime', 'EndTime'});
    writetable(T, fullfile(folder, '频率汇总表.csv'));
    save(fullfile(folder, '完整结果.mat'), 'results');
end 

%% 严格递增处理函数
function x = makeStrictlyIncreasing(x, minDelta)
    for i = 2:length(x)
        if x(i) <= x(i-1)
            requiredDelta = x(i-1) - x(i) + minDelta;
            x(i) = x(i) + requiredDelta;
        end
    end
end