clc; clear; close all;
warning('off', 'MATLAB:table:ModifiedVarnames');

%% 批量处理参数配置
input_folder = 'C:\Users\<USER>\Desktop\snow\tu';   % 输入文件夹路径
min_delta = 1e-9;                                % 严格递增处理参数

%% 初始化环境
excel_files = dir(fullfile(input_folder, '*.xlsx'));

%% 批量处理循环
for fileIdx = 1:length(excel_files)
    current_filename = fullfile(input_folder, excel_files(fileIdx).name);
    [~, workbook_name, ~] = fileparts(current_filename);
    
    fprintf('\n开始处理工作簿: %d/%d | 工作簿名: %s\n', fileIdx, length(excel_files), workbook_name);
    
    [~, sheetList] = xlsfinfo(current_filename);
    results = struct('SheetName', {}, 'MainFreq', {}, 'StartTime', {}, 'EndTime', {});
    
    %% 工作表处理循环
    for sheetIdx = 1:length(sheetList)
        currentSheet = sheetList{sheetIdx};
        fprintf('  处理工作表: %d/%d | 工作表名: %s\n', sheetIdx, length(sheetList), currentSheet);
        
        try 
            %% 数据读取
            opts = detectImportOptions(current_filename, 'Sheet', currentSheet);
            opts.SelectedVariableNames = {'TIME', 'EL_deg', 'SNR_dBHz'};  
            rawData = readtable(current_filename, opts);
            
            %% 数据预处理
            [sinEL, snr_volts, time_valid] = preprocessData(rawData.TIME, rawData.EL_deg, rawData.SNR_dBHz);
            start_time = min(time_valid);
            end_time = max(time_valid);
            
            %% 核心分析
            [zhupin, filtered_signal, sinEL_sorted, imfs, residue] = coreAnalysis(sinEL, snr_volts, min_delta);
            
            %% 结果存储
            results(sheetIdx) = struct( ...
                'SheetName', currentSheet, ...
                'MainFreq', zhupin, ...
                'StartTime', start_time, ...
                'EndTime', end_time ...
            );
            
            %% 生成图表
            if ~isempty(sinEL_sorted) && ~isempty(filtered_signal)
                generateSpectrumPlot(currentSheet, sinEL_sorted, filtered_signal, snr_volts);
            end
            if ~isempty(imfs) && ~isempty(residue)
                % 修改后的函数调用，添加原始信号参数
                generateEMDPlot(currentSheet, imfs, residue, sinEL_sorted, snr_volts); 
            end
            
        catch ME 
            logError(currentSheet, ME.message); 
            results(sheetIdx) = struct( ...
                'SheetName', currentSheet, ...
                'MainFreq', NaN, ...
                'StartTime', NaT, ...
                'EndTime', NaT ...
            );
        end 
    end 
    
    fprintf('  工作簿处理完成: %s\n', workbook_name);
end 

%% 数据预处理函数
function [sinEL, snr_volts, time_valid] = preprocessData(Time, EL_deg, SNR_dBHz)
    if ~isdatetime(Time)
        try
            Time = datetime(Time, 'ConvertFrom', 'excel');
        catch
            error('时间列格式无法解析');
        end
    end
    validIdx = ~isnat(Time) & ~isnan(EL_deg) & ~isnan(SNR_dBHz);
    if sum(validIdx) < 2
        error('有效数据不足（至少需要2个点）');
    end
    sinEL = sind(EL_deg(validIdx));
    snr_volts = 10.^(SNR_dBHz(validIdx)/20);
    time_valid = Time(validIdx);
    
    % 统一排序处理
    [sinEL, sortIdx] = sort(sinEL);
    snr_volts = snr_volts(sortIdx);
    time_valid = time_valid(sortIdx);
end

%% 核心分析函数
function [zhupin, filtered_signal, sinEL_sorted, imfs, residue] = coreAnalysis(sinEL, snr_volts, min_delta)
    zhupin = NaN;
    filtered_signal = [];
    sinEL_sorted = [];
    imfs = [];
    residue = [];
    
    try
        %% 严格递增处理
        sinEL_sorted = makeStrictlyIncreasing(sinEL, min_delta);
        
        %% 验证数据排序
        assert(issorted(sinEL_sorted), 'sinEL必须严格递增排序');
        assert(all(diff(sinEL_sorted) > 0), 'sinEL必须严格递增');
        
        %% EMD分解原始信号
        [imfs, residue] = emd(snr_volts, 'Interpolation', 'pchip');
        
        %% 筛选有效IMF分量
        if isempty(imfs)
            error('EMD分解未产生有效IMF分量');
        end
        
        % 计算每个IMF与总和的相关系数
        sum_imfs = sum(imfs, 2);
        correlations = arrayfun(@(i) corr(imfs(:,i), sum_imfs), 1:size(imfs,2));
        valid_imfs = imfs(:, correlations > 0.5);
        
        %% 重构信号
        if isempty(valid_imfs)
            filtered_signal = sum_imfs; % 使用全部IMF
        else
            filtered_signal = sum(valid_imfs, 2);
        end
        
        %% 频谱分析
        f_custom = 0:0.1:400;
        [P, f] = plomb(filtered_signal, sinEL_sorted, f_custom);
        [~, maxIdx] = max(P);
        zhupin = f(maxIdx);
        
    catch ME
        disp(['分析错误: ' ME.message]);
    end
end

%% 严格递增处理函数
function x = makeStrictlyIncreasing(x, minDelta)
    for i = 2:length(x)
        if x(i) <= x(i-1)
            requiredDelta = x(i-1) - x(i) + minDelta;
            x(i) = x(i) + requiredDelta;
        end
    end
end

%% 频谱图生成函数（三图布局）
function generateSpectrumPlot(sheetName, sinEL_sorted, filtered_signal, original_signal)
    fig = figure('Position', [100 100 900 800]);
    
    % 原始信号子图
    subplot(3,1,1)
    plot(sinEL_sorted, original_signal, 'b', 'LineWidth', 1)
    title(sprintf('%s 原始信号', sheetName), 'Interpreter', 'none')
    xlabel('sin(EL)'), ylabel('Amplitude (V)')
    grid on
    xlim([min(sinEL_sorted) max(sinEL_sorted)])
    
    % 重构信号子图
    subplot(3,1,2)
    plot(sinEL_sorted, filtered_signal, 'r', 'LineWidth', 1)
    title(sprintf('%s 重构信号', sheetName), 'Interpreter', 'none')
    xlabel('sin(EL)'), ylabel('Amplitude (V)')
    grid on
    xlim([min(sinEL_sorted) max(sinEL_sorted)])
    
    % 频谱分析子图
    subplot(3,1,3)
    f_custom = 0:0.1:400;
    [P, f] = plomb(filtered_signal, sinEL_sorted, f_custom);
    plot(f, P, 'k', 'LineWidth', 1)
    xlim([0 400])
    xlabel('Frequency (Hz)')
    ylabel('Power')
    grid on
    
    % 显示图像
    set(findall(fig, 'Type', 'axes'), 'FontSize', 9)
    set(fig, 'Color', 'w')
end

%% EMD可视化函数（修改版）
function generateEMDPlot(sheetName, imfs, residue, sinEL_sorted, original_signal)
     fig_decomposition = figure('Visible', 'on', 'Color', 'w');
 % 增加高度以容纳更多子图
    numIMFs = size(imfs, 2);
    
    % 绘制原始信号（新增子图）
    subplot(numIMFs+2, 1, 1);
    plot(sinEL_sorted, original_signal, 'R', 'LineWidth', 1);
%     title(sprintf('%s 原始信号', sheetName), 'Interpreter', 'none');
%     xlabel('sin(EL)'),
    ylabel('SNR',"FontSize",12);
    grid on;
    xlim([0.05,0.45]);
    set(gca, 'XTickLabel', []); % 不显示当前子图的横坐标刻度
    % 绘制IMF分量（调整子图位置）
    for i = 1:numIMFs
        subplot(numIMFs+2, 1, i+1);
        plot(sinEL_sorted, imfs(:,i), 'black','LineWidth', 0.5);
        ylabel(['IMF ', num2str(i)],"FontSize",12);
        grid on;
        xlim([0.05,0.45]); % 统一X轴范围
    set(gca, 'XTickLabel', []); % 不显示当前子图的横坐标刻度
    end
    
    % 绘制残差（调整子图位置）
    subplot(numIMFs+2, 1, numIMFs+2);
    plot(sinEL_sorted, residue, 'g','LineWidth', 0.5);
    ylabel('RESIDUAL',"FontSize",12);
    xlabel('sinEL',"FontSize",12);
    grid on;
    xlim([0.05,0.45]);
     set(fig_decomposition, 'Position', [100, 10, 900, 600]);
%     sgtitle([sheetName ' - EMD分解结果'], 'Interpreter', 'none');
end

%% 错误日志函数
function logError(sheet, msg)
    fprintf('[Error] %s: %s\n', sheet, msg);
end