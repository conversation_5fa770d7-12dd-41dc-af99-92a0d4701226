clc; clear; close all;
warning('off', 'MATLAB:table:ModifiedVarnames');

%% 添加并行池配置（可选）
if isempty(gcp('nocreate'))
    parpool; % 自动检测可用核心数
end

%% 批量处理参数配置
input_folder = 'D:\excelsnow\P3512020KECHULI';
output_root = 'D:\excelsnow\P3512020VMD';
min_delta = 1e-9;

%% VMD特有参数配置
vmd_params.num_imfs = 4;       % 模态数
vmd_params.alpha = 1000;       % 惩罚因子

%% 初始化环境
if ~exist(output_root, 'dir')
    mkdir(output_root);
end
excel_files = dir(fullfile(input_folder, '*.xlsx'));

%% 并行处理循环
parfor fileIdx = 1:length(excel_files)
    current_filename = fullfile(input_folder, excel_files(fileIdx).name);
    [~, workbook_name, ~] = fileparts(current_filename);
    output_folder = fullfile(output_root, workbook_name);
    
    fprintf('\n开始处理工作簿: %d/%d | 工作簿名: %s\n', fileIdx, length(excel_files), workbook_name);
    
    if ~exist(output_folder, 'dir')
        mkdir(output_folder);
    end
    
    [~, sheetList] = xlsfinfo(current_filename);
    results = struct('SheetName', {}, 'MainFreq', {}, 'StartTime', {}, 'EndTime', {});
    
    %% 工作表处理循环
    for sheetIdx = 1:length(sheetList)
        currentSheet = sheetList{sheetIdx};
        fprintf('  处理工作表: %d/%d | 工作表名: %s\n', sheetIdx, length(sheetList), currentSheet);
        
        try 
            %% 数据读取
            opts = detectImportOptions(current_filename, 'Sheet', currentSheet);
            opts.SelectedVariableNames = {'TIME', 'EL_deg', 'SNR_dBHz'};  
            rawData = readtable(current_filename, opts);
            
            %% 数据预处理
            [sinEL, snr_volts, time_valid] = preprocessData(rawData.TIME, rawData.EL_deg, rawData.SNR_dBHz);
            start_time = min(time_valid);
            end_time = max(time_valid);
            
            %% 核心分析（使用VMD）
            [zhupin, filtered_signal, sinEL_sorted, imfs, residue] = coreAnalysisVMD(...
                sinEL, snr_volts, min_delta, vmd_params);
            
            %% 结果存储
            results(sheetIdx) = struct( ...
                'SheetName', currentSheet, ...
                'MainFreq', zhupin, ...
                'StartTime', start_time, ...
                'EndTime', end_time ...
            );
            
            %% 生成图表
            if ~isempty(sinEL_sorted) && ~isempty(filtered_signal)
                generateSpectrumPlot(currentSheet, sinEL_sorted, filtered_signal, snr_volts, output_folder);
            end
            if ~isempty(imfs) && ~isempty(residue)
                generateVMDPlot(currentSheet, imfs, residue, sinEL_sorted, output_folder);
            end
            
        catch ME 
            logError(output_folder, currentSheet, ME.message); 
            results(sheetIdx) = struct( ...
                'SheetName', currentSheet, ...
                'MainFreq', NaN, ...
                'StartTime', NaT, ...
                'EndTime', NaT ...
            );
        end 
    end 
    
    %% 导出汇总表
    exportWorkbookSummary(results, output_folder, workbook_name);
    fprintf('  工作簿处理完成: %s\n', workbook_name);
end 

%% 数据预处理函数（保持不变）
function [sinEL, snr_volts, time_valid] = preprocessData(Time, EL_deg, SNR_dBHz)
    if ~isdatetime(Time)
        try
            Time = datetime(Time, 'ConvertFrom', 'excel');
        catch
            error('时间列格式无法解析');
        end
    end
    validIdx = ~isnat(Time) & ~isnan(EL_deg) & ~isnan(SNR_dBHz);
    if sum(validIdx) < 2
        error('有效数据不足（至少需要2个点）');
    end
    sinEL = sind(EL_deg(validIdx));
    snr_volts = 10.^(SNR_dBHz(validIdx)/20);
    time_valid = Time(validIdx);
    
    % 统一排序处理
    [sinEL, sortIdx] = sort(sinEL);
    snr_volts = snr_volts(sortIdx);
    time_valid = time_valid(sortIdx);
end

%% 核心分析函数
function [zhupin, filtered_signal, sinEL_sorted, imfs, residue] = coreAnalysisVMD(...
    sinEL, snr_volts, min_delta, vmd_params)
    
    zhupin = NaN;
    filtered_signal = [];
    sinEL_sorted = [];
    imfs = [];
    residue = [];
    
    try
        %% 严格递增处理
        sinEL_sorted = makeStrictlyIncreasing(sinEL, min_delta);
        
        %% 验证排序
        assert(issorted(sinEL_sorted), 'sinEL必须严格递增排序');
        assert(all(diff(sinEL_sorted) > 0), 'sinEL必须严格递增');
        
        %% VMD分解
        [imfs, ~] = vmd(snr_volts(:), ...
            'NumIMFs', vmd_params.num_imfs, ...
            'PenaltyFactor', vmd_params.alpha);
        
        %% 重构信号（假设前4个模态为有效信号）
        if size(imfs,2) >= 3
            filtered_signal = sum(imfs(:,1:3), 2); % 前n-1个模态为有效分量
            residue = imfs(:,4); % 最后一个模态为残差
        else
            error
        end
        
        %% 频谱分析
        f_custom = 0:0.1:400;
        [P, f] = plomb(filtered_signal, sinEL_sorted, f_custom);
        [~, maxIdx] = max(P);
        zhupin = f(maxIdx);
        
    catch ME
        disp(['VMD分析错误: ' ME.message]);
    end
end

%% 严格递增处理函数（保持不变）
function x = makeStrictlyIncreasing(x, minDelta)
    for i = 2:length(x)
        if x(i) <= x(i-1)
            requiredDelta = x(i-1) - x(i) + minDelta;
            x(i) = x(i) + requiredDelta;
        end
    end
end

%% 频谱图生成函数（保持不变）
function generateSpectrumPlot(sheetName, sinEL_sorted, filtered_signal, original_signal, output_folder)
    fig = figure('Visible', 'off', 'Position', [100 100 900 800]);
    
    subplot(3,1,1)
    plot(sinEL_sorted, original_signal, 'b', 'LineWidth', 1)
    title(sprintf('%s 原始信号', sheetName), 'Interpreter', 'none')
    xlabel('sin(EL)'), ylabel('Amplitude (V)')
    grid on
    
    subplot(3,1,2)
    plot(sinEL_sorted, filtered_signal, 'r', 'LineWidth', 1)
    title(sprintf('%s VMD重构信号', sheetName), 'Interpreter', 'none')
    xlabel('sin(EL)'), ylabel('Amplitude (V)')
    grid on
    
    subplot(3,1,3)
    f_custom = 0:0.1:400;
    [P, f] = plomb(filtered_signal, sinEL_sorted, f_custom);
    plot(f, P, 'k', 'LineWidth', 1)
    xlim([0 400])
    xlabel('Frequency (Hz)')
    ylabel('Power')
    grid on
    
    set(findall(fig, 'Type', 'axes'), 'FontSize', 9)
    set(fig, 'Color', 'w')
    saveas(fig, fullfile(output_folder, [sheetName '_频谱分析.png']));
    close(fig);
end

%% VMD模态图生成函数（替换原EMD绘图）
function generateVMDPlot(sheetName, imfs, residue, sinEL_sorted, output_folder)
    fig = figure('Visible', 'off', 'Position', [100, 100, 800, 600]);
    numIMFs = size(imfs, 2);
    
    for i = 1:numIMFs
        subplot(numIMFs+1, 1, i);
        plot(sinEL_sorted, imfs(:,i), 'LineWidth', 0.5);
        ylabel(['IMF ', num2str(i)]);
        grid on;
    end
    
    subplot(numIMFs+1, 1, numIMFs+1);
    plot(sinEL_sorted, residue, 'LineWidth', 0.5);
    ylabel('Residue');
    xlabel('sin(EL)');
    grid on;
    
    sgtitle([sheetName ' - VMD模态分解结果'], 'Interpreter', 'none');
    saveas(fig, fullfile(output_folder, [sheetName '_VMD分解.png']));
    close(fig);
end

%% 错误日志函数（保持不变）
function logError(folder, sheet, msg)
    fid = fopen(fullfile(folder, 'error_log.txt'),  'a');
    fprintf(fid, '[%s] %s: %s\n', datestr(now, 'yyyy-mm-dd HH:MM'), sheet, msg);
    fclose(fid);
end

%% 汇总表导出函数（保持不变）
function exportWorkbookSummary(results, folder, workbook_name)
    sheetNames = {results.SheetName}';
    mainFreqs = [results.MainFreq]';
    startTimes = {results.StartTime}';
    endTimes = {results.EndTime}';
    
    T = table(sheetNames, mainFreqs, startTimes, endTimes, ...
        'VariableNames', {'Sheet', 'MainFrequency', 'StartTime', 'EndTime'});
    
    writetable(T, fullfile(folder, sprintf('%s_频率汇总表.csv', workbook_name)));
end