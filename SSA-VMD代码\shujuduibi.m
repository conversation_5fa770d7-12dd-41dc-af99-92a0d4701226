% 读取 Excel 文件
data = readtable('C:\Users\<USER>\Desktop\SSA-VMD代码\COFA2_snow_cover_plot_1_新版_完整.xlsx');

% 获取列名
column_names = data.Properties.VariableNames;

% 获取表的列数
num_columns = width(data);

% 创建图形窗口
figure('Color', 'white', 'Position', [100 100 800 600]);

% 绘制 B 列（实测数据），使用较粗的线突出显示
time_A = data{:, 1}; % A 列时间
measured_data_B = data{:, 2}; % B 列实测数据
plot(time_A, measured_data_B, 'LineWidth', 2, 'DisplayName', column_names{2});
hold on;

% 循环绘制其他方法的数据曲线
for i = 3:2:num_columns
    time = data{:, i}; % 提取时间列
    values = data{:, i + 1}; % 提取对应的数据列
    method_name = column_names{i + 1};
    
    % 绘制曲线
    plot(time, values, 'DisplayName', method_name);
end

% 设置图形属性
xlabel('TIME');
ylabel('SNOW DEPTH(CM)');
% title('不同方法数据与实测数据对比');
grid on;
legend;
hold off;