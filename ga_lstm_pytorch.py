"""
GA-LSTM模型实现 - 四个输入，一个输出（PyTorch版本）
使用遗传算法优化LSTM超参数
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
import random
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子
def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)

set_seed(42)

class LSTMModel(nn.Module):
    """四输入单输出LSTM模型"""
    def __init__(self, input_size=4, hidden_size=50, num_layers=1, output_size=1, dropout=0.2):
        super(LSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, 
                           batch_first=True, dropout=dropout if num_layers > 1 else 0)
        self.dropout = nn.Dropout(dropout)
        self.fc1 = nn.Linear(hidden_size, 50)
        self.relu = nn.ReLU()
        self.fc2 = nn.Linear(50, output_size)
        
    def forward(self, x):
        # x shape: (batch_size, seq_length, input_size)
        lstm_out, _ = self.lstm(x)
        # 取最后一个时间步的输出
        last_output = lstm_out[:, -1, :]
        out = self.dropout(last_output)
        out = self.fc1(out)
        out = self.relu(out)
        out = self.fc2(out)
        return out

def generate_tide_data(days=50, samples_per_day=96, noise_level=0.1):
    """
    模拟海洋潮位数据（含日潮、半日潮、长期趋势）
    
    参数:
        days: 模拟天数 (int)
        samples_per_day: 每天采样点数（如96 = 每15分钟一个点）
        noise_level: 添加噪声强度
        
    返回:
        data1 ~ data4: 四个不同位置的潮位观测
        target: 目标位置潮位
    """
    total_samples = days * samples_per_day
    t = np.linspace(0, days * 2 * np.pi, total_samples)  # 时间向量
    
    # 日潮（周期为一天）
    daily_tide = np.sin(t)
    
    # 半日潮（周期为半天）
    semi_daily_tide = 0.6 * np.sin(2 * t)
    
    # 长期趋势（代表月球影响等）
    long_term_trend = 0.1 * np.sin(t / 5)
    
    # 总体潮位 = 各分量叠加
    tide_base = daily_tide + semi_daily_tide + long_term_trend
    
    # 不同位置的潮位观测（加相位差和幅度差异）
    data1 = tide_base + noise_level * np.random.randn(len(t))  # 基础观测
    data2 = 0.95 * tide_base + 0.1 * np.sin(t + np.pi/4) + noise_level * np.random.randn(len(t))
    data3 = 0.85 * tide_base + 0.1 * np.cos(t + np.pi/2) + noise_level * np.random.randn(len(t))
    data4 = 0.75 * tide_base + 0.1 * np.sin(t + np.pi/3) + noise_level * np.random.randn(len(t))
    
    # 输出为目标位置潮位（带偏移和噪声）
    target = 0.9 * tide_base + 0.1 * np.sin(t + np.pi/6) + noise_level * np.random.randn(len(t))
    
    return data1, data2, data3, data4, target

def prepare_data(data1, data2, data3, data4, target, time_steps=20):
    """准备序列数据"""
    # 数据归一化
    scaler_inputs = MinMaxScaler()
    scaler_target = MinMaxScaler()
    
    # 合并输入数据进行归一化
    inputs = np.column_stack([data1, data2, data3, data4])
    inputs_scaled = scaler_inputs.fit_transform(inputs)
    target_scaled = scaler_target.fit_transform(target.reshape(-1, 1)).flatten()
    
    # 创建序列数据
    X, y = [], []
    for i in range(len(inputs_scaled) - time_steps):
        X.append(inputs_scaled[i:i+time_steps])
        y.append(target_scaled[i+time_steps])
    
    X = np.array(X)  # shape: (samples, time_steps, features)
    y = np.array(y)  # shape: (samples,)
    
    return X, y, scaler_inputs, scaler_target

def split_data(X, y, train_ratio=0.6, val_ratio=0.2):
    """划分训练集、验证集和测试集"""
    n_samples = len(X)
    train_size = int(n_samples * train_ratio)
    val_size = int(n_samples * val_ratio)
    
    X_train = X[:train_size]
    y_train = y[:train_size]
    X_val = X[train_size:train_size+val_size]
    y_val = y[train_size:train_size+val_size]
    X_test = X[train_size+val_size:]
    y_test = y[train_size+val_size:]
    
    return X_train, y_train, X_val, y_val, X_test, y_test

def train_model(model, X_train, y_train, X_val, y_val, epochs=100, batch_size=32, lr=0.001):
    """训练模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # 转换为PyTorch张量
    X_train_tensor = torch.FloatTensor(X_train).to(device)
    y_train_tensor = torch.FloatTensor(y_train).to(device)
    X_val_tensor = torch.FloatTensor(X_val).to(device)
    y_val_tensor = torch.FloatTensor(y_val).to(device)
    
    # 创建数据加载器
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    
    # 优化器和损失函数
    optimizer = optim.Adam(model.parameters(), lr=lr)
    criterion = nn.MSELoss()
    
    # 训练循环
    train_losses = []
    val_losses = []
    
    for epoch in range(epochs):
        model.train()
        train_loss = 0
        for batch_X, batch_y in train_loader:
            optimizer.zero_grad()
            outputs = model(batch_X).squeeze()
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            train_loss += loss.item()
        
        # 验证
        model.eval()
        with torch.no_grad():
            val_outputs = model(X_val_tensor).squeeze()
            val_loss = criterion(val_outputs, y_val_tensor).item()
        
        train_losses.append(train_loss / len(train_loader))
        val_losses.append(val_loss)
        
        if epoch % 20 == 0:
            print(f'Epoch {epoch}/{epochs}, Train Loss: {train_losses[-1]:.6f}, Val Loss: {val_loss:.6f}')
    
    return model, train_losses, val_losses

def evaluate_model(model, X_test, y_test, scaler_target):
    """评估模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.eval()
    
    X_test_tensor = torch.FloatTensor(X_test).to(device)
    
    with torch.no_grad():
        y_pred = model(X_test_tensor).squeeze().cpu().numpy()
    
    # 反归一化
    y_test_actual = scaler_target.inverse_transform(y_test.reshape(-1, 1)).flatten()
    y_pred_actual = scaler_target.inverse_transform(y_pred.reshape(-1, 1)).flatten()
    
    # 计算评估指标
    mse = mean_squared_error(y_test_actual, y_pred_actual)
    mae = mean_absolute_error(y_test_actual, y_pred_actual)
    rmse = np.sqrt(mse)
    r2 = r2_score(y_test_actual, y_pred_actual)
    
    return {
        'mse': mse,
        'mae': mae,
        'rmse': rmse,
        'r2': r2,
        'y_true': y_test_actual,
        'y_pred': y_pred_actual
    }

# 遗传算法相关函数
class GAOptimizer:
    """遗传算法优化器"""
    def __init__(self, param_space, population_size=10, generations=5):
        self.param_space = param_space
        self.population_size = population_size
        self.generations = generations
        self.population = self.initialize_population()
        self.best_individual = None
        self.best_fitness = float('inf')
        
    def initialize_population(self):
        """初始化种群"""
        population = []
        for _ in range(self.population_size):
            individual = {}
            for param, values in self.param_space.items():
                individual[param] = random.choice(values)
            population.append(individual)
        return population
    
    def fitness_function(self, individual, X_train, y_train, X_val, y_val):
        """适应度函数"""
        try:
            # 创建模型
            model = LSTMModel(
                input_size=4,
                hidden_size=individual['hidden_size'],
                num_layers=individual['num_layers'],
                dropout=individual['dropout']
            )
            
            # 训练模型（减少epochs用于快速评估）
            model, _, _ = train_model(
                model, X_train, y_train, X_val, y_val,
                epochs=individual['epochs']//2,  # 减少训练时间
                batch_size=individual['batch_size'],
                lr=individual['learning_rate']
            )
            
            # 评估
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            model.eval()
            X_val_tensor = torch.FloatTensor(X_val).to(device)
            y_val_tensor = torch.FloatTensor(y_val).to(device)
            
            with torch.no_grad():
                y_pred = model(X_val_tensor).squeeze()
                mse = nn.MSELoss()(y_pred, y_val_tensor).item()
            
            return mse
            
        except Exception as e:
            print(f"训练失败: {e}")
            return 1e6  # 返回很大的值表示失败
    
    def selection(self, fitness_scores):
        """选择操作"""
        # 锦标赛选择
        parents = []
        for _ in range(self.population_size // 2):
            candidates = random.sample(list(zip(self.population, fitness_scores)), 3)
            winner = min(candidates, key=lambda x: x[1])
            parents.append(winner[0])
        return parents
    
    def crossover(self, parent1, parent2):
        """交叉操作"""
        child = {}
        for param in self.param_space.keys():
            child[param] = random.choice([parent1[param], parent2[param]])
        return child
    
    def mutation(self, individual, mutation_rate=0.1):
        """变异操作"""
        for param, values in self.param_space.items():
            if random.random() < mutation_rate:
                individual[param] = random.choice(values)
        return individual
    
    def evolve(self, X_train, y_train, X_val, y_val):
        """进化过程"""
        for generation in range(self.generations):
            print(f"\nGeneration {generation + 1}/{self.generations}")
            
            # 评估适应度
            fitness_scores = []
            for i, individual in enumerate(self.population):
                fitness = self.fitness_function(individual, X_train, y_train, X_val, y_val)
                fitness_scores.append(fitness)
                print(f"Individual {i+1}: {individual}, Fitness: {fitness:.6f}")
                
                # 更新最佳个体
                if fitness < self.best_fitness:
                    self.best_fitness = fitness
                    self.best_individual = individual.copy()
            
            # 选择
            parents = self.selection(fitness_scores)
            
            # 创建新一代
            new_population = []
            
            # 精英保留
            best_idx = fitness_scores.index(min(fitness_scores))
            new_population.append(self.population[best_idx])
            
            # 交叉和变异
            while len(new_population) < self.population_size:
                parent1, parent2 = random.sample(parents, 2)
                child = self.crossover(parent1, parent2)
                child = self.mutation(child)
                new_population.append(child)
            
            self.population = new_population
            print(f"Best fitness so far: {self.best_fitness:.6f}")
        
        return self.best_individual, self.best_fitness

def main():
    """主函数"""
    print("GA-LSTM模型训练开始...")
    
    # 1. 生成数据
    # 1. 生成数据
    print("1. 生成潮位数据...")
    days = 50            # 模拟5天的数据
    samples_per_day = 96  # 每15分钟一个点
    noise_level = 0.1    # 噪声水平

    data1, data2, data3, data4, target = generate_tide_data(days, samples_per_day, noise_level)
    
    # 2. 数据预处理
    print("2. 数据预处理...")
    X, y, scaler_inputs, scaler_target = prepare_data(data1, data2, data3, data4, target)
    print(f"数据形状: X={X.shape}, y={y.shape}")
    
    # 3. 划分数据
    X_train, y_train, X_val, y_val, X_test, y_test = split_data(X, y)
    print(f"训练集: {X_train.shape}, 验证集: {X_val.shape}, 测试集: {X_test.shape}")
    
    # 4. 定义参数搜索空间
    param_space = {
        'hidden_size': [32, 64, 128],
        'num_layers': [1, 2],
        'dropout': [0.1, 0.2, 0.3],
        'learning_rate': [0.001, 0.01],
        'batch_size': [16, 32, 64],
        'epochs': [50, 100]
    }
    
    # 5. 遗传算法优化
    print("3. 开始遗传算法优化...")
    ga = GAOptimizer(param_space, population_size=8, generations=3)
    best_params, best_fitness = ga.evolve(X_train, y_train, X_val, y_val)
    
    print(f"\n最佳参数: {best_params}")
    print(f"最佳适应度: {best_fitness:.6f}")
    
    # 6. 使用最佳参数训练最终模型
    print("4. 训练最终模型...")
    final_model = LSTMModel(
        input_size=4,
        hidden_size=best_params['hidden_size'],
        num_layers=best_params['num_layers'],
        dropout=best_params['dropout']
    )
    
    final_model, train_losses, val_losses = train_model(
        final_model, X_train, y_train, X_val, y_val,
        epochs=best_params['epochs'],
        batch_size=best_params['batch_size'],
        lr=best_params['learning_rate']
    )
    
    # 7. 评估模型
    print("5. 评估模型...")
    results = evaluate_model(final_model, X_test, y_test, scaler_target)
    
    print(f"\n测试结果:")
    print(f"MSE: {results['mse']:.4f}")
    print(f"MAE: {results['mae']:.4f}")
    print(f"RMSE: {results['rmse']:.4f}")
    print(f"R²: {results['r2']:.4f}")
    
    # 8. 可视化结果
    plt.figure(figsize=(15, 10))
    
    # 训练损失
    plt.subplot(2, 3, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Val Loss')
    plt.title('Training History')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)
    
    # 预测结果
    plt.subplot(2, 3, 2)
    plt.plot(results['y_true'], label='True', linewidth=1.5)
    plt.plot(results['y_pred'], label='Predicted', linewidth=1.5, linestyle='--')
    plt.title('True vs Predicted')
    plt.xlabel('Time')
    plt.ylabel('Value')
    plt.legend()
    plt.grid(True)
    
    # 散点图
    plt.subplot(2, 3, 3)
    plt.scatter(results['y_true'], results['y_pred'], alpha=0.6)
    plt.plot([results['y_true'].min(), results['y_true'].max()], 
             [results['y_true'].min(), results['y_true'].max()], 'r--', linewidth=2)
    plt.title('True vs Predicted Scatter')
    plt.xlabel('True Values')
    plt.ylabel('Predicted Values')
    plt.grid(True)
    
    # 误差分布
    plt.subplot(2, 3, 4)
    errors = results['y_true'] - results['y_pred']
    plt.hist(errors, bins=30, alpha=0.7)
    plt.title('Error Distribution')
    plt.xlabel('Prediction Error（m）')
    plt.ylabel('Frequency')
    plt.grid(True)
    
    # 误差时间序列
    plt.subplot(2, 3, 5)
    plt.plot(errors)
    plt.title('Prediction Error Over Time')
    plt.xlabel('Time')
    plt.ylabel('Error')
    plt.axhline(y=0, color='r', linestyle='--')
    plt.grid(True)
    
    # 输入数据可视化
    # 每天取一个点（对应索引间隔为 96）
    t_daily = np.arange(0, len(data1), 96)
    plt.plot(t_daily, data1[::96], label='Daily Trend', marker='o', linestyle='-', markersize=4)
    plt.title('Daily Tide Trend (50 Days)')
    plt.xlabel('Day')
    plt.ylabel('Tide Level (m)')
    plt.grid(True)
    plt.tight_layout()
    plt.show()
    
    print("\n模型训练和评估完成！")

if __name__ == "__main__":
    main()
