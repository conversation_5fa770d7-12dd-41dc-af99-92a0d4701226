% 读取 Excel 文件
data = readtable('ploy.xlsx');

% 提取数据列（假设第二列为 Var2，第三列为 langgao）
Var2 = data.Var2;
langgao = data.h;

% 计算残差（第二列减去第三列）
residuals =langgao- Var2  ;

% 绘制散点图
figure('Color', 'white', 'Position', [100 100 800 600]); % 设置图形窗口大小和背景色
scatter(residuals, Var2, 30, 'blue', 'filled'); % 纯红色散点
% 添加垂直虚线 x = -0.5 和 x = 0.5
xline(-0.5, '--', 'Color', 'k', 'LineWidth', 1.2, 'Label', ''); % 左虚线
xline(0.5, '--', 'Color', 'k', 'LineWidth', 1.2, 'Label', '');  % 右虚线
% 设置坐标轴标签和标题
xlabel('残差（m）', 'FontSize', 12);
ylabel('潮位(m)', 'FontSize', 12);
% title('第二列与残差的散点图', 'FontSize', 14, 'FontWeight', 'bold');
ax = gca;
xlims = xlim;
ylims = ylim;
text(xlims(2), ylims(2), '传统方法', 'HorizontalAlignment', 'right', 'VerticalAlignment', 'bottom', 'FontSize', 14);
% 添加网格线
% grid on;
% grid minor;
% set(gca, 'GridLineStyle', '--', 'GridAlpha', 0.7); % 虚线网格，透明度0.7

% 调整坐标轴刻度和布局
box on; % 显示图形边框
set(gca, 'FontSize', 11); % 设置坐标轴字体大小