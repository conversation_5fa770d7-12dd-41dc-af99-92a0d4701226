% 读取 Excel 文件数据
data = readtable('flt.xlsx'); 

% 提取所需的列
time_col1 = data{:, 1};
value_col2 = data{:, 2};
time_col3 = data{:, 9};
value_col4 = data{:,10};

% 定义起始日期
start_date = datetime(2023, 8, 1);
start_num = 232;

% 定义一个函数用于将日期转换为指定数字
convert_date_to_num = @(date) start_num + days(date - start_date);

% 将时间列转换为指定数字
converted_col1 = convert_date_to_num(datetime(time_col1));
converted_col3 = convert_date_to_num(datetime(time_col3));

% 处理第二列数据，将小于 0 和大于 5 的值转换为 5
value_col2(value_col2 < 0 | value_col2 > 5) = 5;
% 处理第四列数据，将小于 0 和大于 5 的值转换为 5
value_col4(value_col4 < 0 | value_col4 > 5) = 5;

% 绘制第一列和第二列的折线图，设置线条宽度为 2
figure;
plot(converted_col1, value_col2, 'b-', 'LineWidth', 2);
hold on;

% 在同一图上绘制第三列对应时间的第四列散点图，设置红点大小为 200
scatter(converted_col3, value_col4, 200, 'r.');

% 设置图形标题和坐标轴标签
% title('潮位数据对比图');
% xlabel('转换后的日期数字');
% ylabel('Values');

% 设置纵坐标范围为 0 到 5
ylim([0 5]);
% yticks([0,5])
% 获取当前坐标轴范围
ax = gca;
xlims = ax.XLim;
ylims = ax.YLim;

% 在右上角显示 SSA - VMD
text(xlims(2), ylims(2), 'VMD-6', 'HorizontalAlignment', 'right', 'VerticalAlignment', 'top','FontSize', 14);

% 确保图形布局合理
grid on;
hold off;