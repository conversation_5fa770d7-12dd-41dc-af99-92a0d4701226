% ===== 配置参数 =====
input_folder = 'C:\Users\<USER>\Desktop\snow\tu';  % 输入文件夹路径

% 获取所有Excel文件（包含.xls和.xlsx）
file_list = dir(fullfile(input_folder, '*.xls*'));

for file_idx = 1:numel(file_list)
    try
        input_file = fullfile(file_list(file_idx).folder, file_list(file_idx).name);
        [~, filename, ~] = fileparts(input_file);
        fprintf('处理文件: %s (%d/%d)\n', filename, file_idx, numel(file_list));

        % 获取工作表列表
        [~, sheets] = xlsfinfo(input_file);
        
        for sheet_idx = 1:numel(sheets)
            sheet_name = sheets{sheet_idx};
            fprintf('  处理工作表: %s (%d/%d)\n', sheet_name, sheet_idx, numel(sheets));

            % 读取工作表数据
            data = readtable(input_file, 'Sheet', sheet_name);
            TIME = data.TIME;
            SNR = data.SNR_dBHz;
            EL = data.EL_deg;

            % 时间列转换为datenum格式
            if ~isnumeric(TIME)
                try
                    TIME = datenum(TIME);
                catch
                    TIME = datenum(datetime(TIME, 'InputFormat', 'yyyy-MM-dd HH:mm:ss'));
                end
            end

            % 数据预处理
            sinEL = sind(EL);
            [sinEL_sorted, idx] = sort(sinEL);
            TIME_sorted = TIME(idx);
            SNR_sorted = 10.^(SNR(idx)/20);
            minDelta = 1e-6;
            sinEL_strict = makeStrictlyIncreasing(sinEL_sorted, minDelta);
            
            % 标准化处理
            SNR_standardized = SNR_sorted(:) / std(SNR_sorted(:));
            x = SNR_standardized;
            desvio_x = std(x) * std(SNR_sorted(:));  % 用于恢复原始尺度

            % 参数设置
            Nstd = 0.2;
            NR = 100;
            MaxIter = 1000;
            SNRFlag = 2;

            % 初始化变量
            num_samples = length(x);
            modes = zeros(num_samples, 0);
            residue = x;
            aux = zeros(num_samples, 1);

            % 生成高斯白噪声并预分解
            white_noise = cell(NR, 1);
            noise_modes = cell(NR, 1);
            for i = 1:NR
                white_noise{i} = randn(num_samples, 1);
                noise_modes{i} = emd(white_noise{i});
            end

            % CEEMDAN主分解循环
            k = 1;
            while true
                if k == 1
                    for i = 1:NR
                        imf_noise = noise_modes{i}(1, :)';
                        imf_noise = imf_noise / std(imf_noise);
                        noisy_signal = residue + Nstd * imf_noise;
                        [temp, ~, ~] = emd(noisy_signal, 'MAXMODES', 1, 'MAXITERATIONS', MaxIter);
                        imf = temp(1, :)';
                        aux = aux + (noisy_signal - imf) / NR;
                    end
                else
                    for i = 1:NR
                        if size(noise_modes{i}, 1) >= k
                            imf_noise = noise_modes{i}(k, :)';
                        else
                            imf_noise = zeros(num_samples, 1);
                        end
                        if SNRFlag == 2
                            imf_noise = imf_noise / std(imf_noise);
                        end
                        imf_noise = Nstd * imf_noise;
                        noisy_signal = residue + std(residue) * imf_noise;
                        try
                            [temp, ~] = emd(noisy_signal, 'MAXMODES', 1, 'MAXITERATIONS', MaxIter);
                        catch
                            [temp, ~] = emd(noisy_signal, 'MAXMODES', 1, 'MAXITERATIONS', MaxIter);
                        end
                        imf = temp(end, :)';
                        aux = aux + imf / NR;
                    end
                end

                current_imf = residue - aux;
                modes = [modes, current_imf];
                residue = aux;
                aux = zeros(num_samples, 1);

                if size(emd(residue, 'MAXMODES', 1, 'MAXITERATIONS', MaxIter), 1) <= 1
                    break;
                end
                k = k + 1;
            end
            modes = [modes, residue];  % 添加残差
            modes = modes * desvio_x;  % 恢复原始尺度

% ===== 生成ICEEMDAN分解图 =====
                fig_decomposition = figure('Visible', 'on', 'Color', 'w');

                % 绘制原始信号
                subplot(size(modes,2)+1, 1, 1);

%                 ylim([30,100])
              plot(sinEL_strict, x*desvio_x, 'Color', 'r', 'LineWidth', 1.2);
                 xlim([0.05,0.45])
                ylabel('SNR','FontSize',12);
%                 title(sprintf('ICEEMDAN分解结果 - %s', sheet_name));
                grid on;
                set(gca, 'XTickLabel', []); % 不显示当前子图的横坐标刻度
                % 绘制所有IMF分量
% 绘制所有IMF分量
for i = 1:size(modes,2)-1
    subplot(size(modes,2)+1, 1, i+1);
    % 根据IMF编号设置颜色
    if i == 1 || i == 2
        line_color = 'black'; % 绿色
    elseif i >= 3 && i <= 5
        line_color = 'black'; % 蓝色
    elseif i == 6
        line_color = 'black'; % 洋红
    else
        line_color = 'black'; % 其他情况使用黑色
    end
    plot(sinEL_strict, modes(:,i), line_color, 'LineWidth', 1.2);
    ylabel(['IMF ', num2str(i)],'FontSize',12);
    xlim([0.05,0.45])
    set(gca, 'XTickLabel', []); % 不显示当前子图的横坐标刻度
    grid on;
end
 
                % 绘制残差
                subplot(size(modes,2)+1, 1, size(modes,2)+1);
                plot(sinEL_strict, modes(:,end), 'g', 'LineWidth', 1.2);
                 xlim([0.05,0.45])
                ylabel('RESIDUAL','FontSize',12);
                xlabel('sinEL');
                grid on;

                % 调整子图间距
                set(fig_decomposition, 'Position', [100, 10, 900, 600]);

            % ===== 频谱分析与合成信号图 =====
            imf_sum = sum(modes(:, 1:end-1), 2);
            correlations = corr(modes(:, 1:end-1), imf_sum, 'Type', 'Pearson');
            selected_imfs = modes(:, correlations > 0.5);
            first_component = sum(selected_imfs, 2);

            f_custom = 0:0.1:400;
            [P_first, f_first] = plomb(first_component, sinEL_strict, f_custom);
            [~, maxIdx_first] = max(P_first);
            zhupin_first = f_first(maxIdx_first);

            fig_spectrum = figure;
            set(fig_spectrum, 'Position', [100, 100, 800, 600]);
            
            subplot(2, 1, 1);
            plot(f_first, P_first);
            xlabel('频率 (Hz)');
            ylabel('功率');
            title(sprintf('合并信号频谱 (主频: %.2f Hz)', zhupin_first));
            xlim([f_custom(1), f_custom(end)]);
            grid on;

            subplot(2, 1, 2);
            plot(sinEL_strict, first_component);
            xlabel('sinEL');
            ylabel('幅度');
            title('合成信号（IMF合并结果）');
            xlim([min(sinEL_strict), max(sinEL_strict)]);
            grid on;

        end
    catch ME
        warning('处理时发生错误: %s', ME.message);
        continue;
    end
end

% ===== 辅助函数 =====
function x = makeStrictlyIncreasing(x, minDelta)
    for i = 2:length(x)
        if x(i) <= x(i-1)
            requiredDelta = x(i-1) - x(i) + minDelta;
            x(i) = x(i) + requiredDelta;
        end
    end
end
