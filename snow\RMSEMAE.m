% 设置文件路径
file_path = 'C:\Users\<USER>\Desktop\SSA-VMD代码\COFA2_snow_cover_plot_1_新版_完整.xlsx';

% 读取数据
data = readtable(file_path);

% 获取第二列和第四列数据
y_true = data{:, 2};
y_pred = data{:, "VMD"};

% 计算均方误差（MSE）
mse = mean((y_true - y_pred).^2);

% 计算均方根误差（RMSE）
rmse = sqrt(mse);

% 计算平均绝对误差（MAE）
mae = mean(abs(y_true - y_pred));
corr_coeff = corr(y_true, y_pred);
% 显示结果
fprintf('RMSE: %.4f\n', rmse);
fprintf('MAE: %.4f\n', mae);
fprintf('相关系数: %.4f\n', corr_coeff);