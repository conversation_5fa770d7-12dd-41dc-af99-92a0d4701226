clc; clear; close all;
warning('off', 'MATLAB:table:ModifiedVarnames');

%% 批量处理参数配置 
input_folder = 'D:\ga-lstm\p351';   % 输入文件夹路径
output_base = 'D:\ga-lstm\p351\p351vmd';       % 结果根目录
min_delta = 1e-9;                     % 严格递增处理参数 

% PSO 参数
pop_size = 20;           % 种群大小
max_iter = 15;           % 迭代次数
dim = 2;                 % 维度：[num_imfs, alpha]
lb = [3, 1000];          % 下限
ub = [8, 5000];          % 上限
weight_envelope_entropy = 0.5;
weight_energy_loss = 0.5;

%% 初始化环境 
if ~exist(output_base, 'dir')
    mkdir(output_base);
end

%% 获取所有Excel文件
excel_files = dir(fullfile(input_folder, '*.xlsx'));
excel_files = [excel_files; dir(fullfile(input_folder, '*.xls'))]; % 支持两种格式

%% 主循环：逐个文件处理
for fileIdx = 1:length(excel_files)
    current_file = excel_files(fileIdx).name;
    file_base = erase(current_file, {'.xlsx', '.xls'});
    output_folder = fullfile(output_base, file_base);
    
    if ~exist(output_folder, 'dir')
        mkdir(output_folder);
    end
    
    fprintf('\n开始处理文件: %d/%d | 文件名: %s\n', fileIdx, length(excel_files), current_file);
    
    [~, sheetList] = xlsfinfo(fullfile(input_folder, current_file));
    results = struct('SheetName', {}, 'MainFreq', {}, 'StartTime', {}, 'EndTime', {}, ...
                     'MedianAZ', {}, 'Amplitude', {}, 'Phase', {}, 'Offset', {}, 'OptimalK', {}, 'OptimalAlpha', {});

    %% 工作表处理循环
    for sheetIdx = 1:length(sheetList)
        currentSheet = sheetList{sheetIdx};
        try
            fprintf('  处理工作表: %d/%d | 工作表名: %s\n', sheetIdx, length(sheetList), currentSheet);
            
            % 数据读取（支持 AZ）
            opts = detectImportOptions(fullfile(input_folder, current_file), 'Sheet', currentSheet);
            opts.SelectedVariableNames = {'TIME', 'EL', 'SNR', 'AZ'};
            rawData = readtable(fullfile(input_folder, current_file), opts);
            
            % 数据预处理
            [sinEL, snr_volts, time_valid, az_sorted] = preprocessData(...
                rawData.TIME, rawData.EL, rawData.SNR, rawData.AZ);

            start_time = min(time_valid);
            end_time = max(time_valid);

            % PSO 优化目标函数
            obj_func = @(x)objectiveFunction(x, sinEL, snr_volts, min_delta, ...
                                             weight_envelope_entropy, weight_energy_loss);

            [best_solution, ~] = pso_optimization(obj_func, pop_size, max_iter, dim, lb, ub);
            best_num_imfs = round(best_solution(1));
            best_alpha = best_solution(2);

            % 使用最优参数进行 VMD 分解
            signal = snr_volts(:);
            [imf, ~] = vmd(signal, best_alpha, 0, best_num_imfs, 1e-7, 1 / length(signal), 1e-9);

            % 确保长度一致
            min_len = min(length(signal), size(imf, 2));
            signal = signal(1:min_len);
            imf = imf(:, 1:min_len);

            % 排序同步
            [sinEL_sorted, sort_idx] = make_strictly_increasing(sinEL(1:min_len), min_delta);
            residuals = sum(imf(1:4, :), 1)';
            residuals = residuals(sort_idx);
            sinEL_sorted = sinEL_sorted(sort_idx);

            % Lomb-Scargle 频谱分析
            f_custom = 0:0.1:400;
            [P, f] = plomb(residuals, sinEL_sorted, f_custom);
            [~, maxIdx] = max(P);
            zhupin = f(maxIdx);

            % 正弦波拟合
            A = std(residuals) * 2;
            phi = 0;
            offset = mean(residuals);
            try
                ftol = 1e-6;
                maxiter = 5000;
                params = lsqnonlin(@(params) sinusoidalModel(params, sinEL_sorted, residuals), ...
                                   [A, phi, offset], [], [], ...
                                   optimset('MaxFunEvals', maxiter, 'TolFun', ftol));
                A_fit = params(1);
                phi_fit = params(2);
                offset_fit = params(3);
            catch
                A_fit = NaN;
                phi_fit = NaN;
                offset_fit = NaN;
            end

            % 计算 MedianAZ
            median_az = NaN;
            if ~isempty(az_sorted)
                median_az = median([az_sorted(1), az_sorted(end)]);
            end

            % 结果存储
            results(end+1) = struct(...
                'SheetName', currentSheet, ...
                'MainFreq', zhupin, ...
                'Amplitude', A_fit, ...
                'Phase', phi_fit, ...
                'Offset', offset_fit, ...
                'StartTime', start_time, ...
                'EndTime', end_time, ...
                'MedianAZ', median_az, ...
                'OptimalK', best_num_imfs, ...
                'OptimalAlpha', best_alpha);

            % 可视化
            generateSpectrumPlot(currentSheet, sinEL_sorted, residuals, output_folder, A_fit, phi_fit, offset_fit);

        catch ME
            logError(output_folder, currentSheet, ME.message);
            continue
        end
    end

    % 导出结果
    exportSummary(results, output_folder);
    fprintf('  文件处理完成: %s\n', current_file);
end

fprintf('\n==== 批量分析全部完成 ====\n');
function fitness = objectiveFunction(x, sinEL, snr_volts, minDelta, w_entropy, w_energy)
    num_imfs = round(x(1));
    alpha = x(2);

    try
        signal = snr_volts';
        [imf, ~] = vmd(signal, alpha, 0, num_imfs, 1e-7, 1 / length(signal), 1e-9);
        
        min_len = min(size(signal, 2), size(imf, 2));
        signal = signal(1:min_len);
        imf = imf(:, 1:min_len);

        [sorted_x, idx] = sort(sinEL(1:min_len));
        residuals = signal - imf(end, :)';
        residuals = residuals(idx);

        % 包络熵
        env = abs(hilbert(residuals));
        norm_env = env ./ (sum(env) + 1e-10);
        entropy = -sum(norm_env .* log2(norm_env + 1e-10));

        % 能量损失
        total_energy_signal = sum(signal.^2);
        total_energy_imf = sum(sum(imf.^2, 2)');
        loss = abs(total_energy_signal - total_energy_imf);

        % Fitness
        fitness = w_entropy * entropy + w_energy * loss;
    catch
        fitness = Inf;
    end
end
function [best_x, best_fitness] = pso_optimization(obj_func, pop_size, max_iter, dim, lb, ub)
    X = rand(pop_size, dim);
    for i = 1:dim
        X(:, i) = lb(i) + X(:, i) * (ub(i) - lb(i));
    end
    X(:, 1) = round(X(:, 1));

    V = randi([-1, 1], pop_size, dim);
    personal_best_X = X;
    personal_best_value = arrayfun(@(i)obj_func(X(i, :)), 1:pop_size)';
    global_best_X = X(find(personal_best_value == min(personal_best_value), 1), :);
    global_best_value = min(personal_best_value);

    w = 0.729; c1 = 1.494; c2 = 1.494;

    for iter = 1:max_iter
        for i = 1:pop_size
            r1 = rand(); r2 = rand();
            V(i, :) = w * V(i, :) + c1 * r1 * (personal_best_X(i, :) - X(i, :)) ...
                      + c2 * r2 * (global_best_X - X(i, :));
            X(i, :) = X(i, :) + V(i, :);
            X(i, 1) = round(clamp(X(i, 1), lb(1), ub(1)));
            X(i, 2) = clamp(X(i, 2), lb(2), ub(2));

            fit = obj_func(X(i, :));
            if fit < personal_best_value(i)
                personal_best_value(i) = fit;
                personal_best_X(i, :) = X(i, :);
            end
            if fit < global_best_value
                global_best_value = fit;
                global_best_X = X(i, :);
            end
        end
        fprintf('Iteration %d/%d | Best Fitness: %.6f\n', iter, max_iter, global_best_value);
    end
    best_x = global_best_X;
    best_fitness = global_best_value;
end
function res = sinusoidalModel(params, x, y)
    A = params(1); phi = params(2); offset = params(3);
    res = A * sin(2 * pi * x + phi) + offset - y;
end
function out = clamp(x, lb, ub)
    out = max(min(x, ub), lb);
end
function generateSpectrumPlot(sheet_name, sinEL_sorted, signal, output_folder, A, phi, offset)
    f_custom = 0:0.1:400;
    P = lombscargle(sinEL_sorted, signal, f_custom, normalize=true);
    fig = figure('Visible', 'off');

    subplot(2,1,1);
    plot(sinEL_sorted, signal, 'red');
    title(sprintf('%s 残差分布', sheet_name));
    xlabel('sin(EL)'); ylabel('Signal');

    subplot(2,1,2);
    plot(f_custom, P);
    title(sprintf('%s 频谱', sheet_name));
    xlabel('Frequency (Hz)'); ylabel('Power'); grid on;

    saveas(fig, fullfile(output_folder, sprintf('%s_频谱.png', sheet_name)));
    close(fig);

    if ~isnan(A) && ~isnan(phi) && ~isnan(offset)
        fig = figure('Visible', 'off');
        plot(sinEL_sorted, signal, 'b', 'DisplayName', 'Original');
        hold on;
        fit_signal = A * sin(2*pi*sinEL_sorted + phi) + offset;
        plot(sinEL_sorted, fit_signal, 'r--', 'DisplayName', sprintf('Fit: A=%.4f, φ=%.4f', A, phi));
        legend show;
        title(sprintf('%s 拟合结果', sheet_name));
        xlabel('sin(EL)'); ylabel('Signal'); grid on;
        saveas(fig, fullfile(output_folder, sprintf('%s_拟合.png', sheet_name)));
        close(fig);
    end
end
function logError(output_folder, sheet_name, error_msg)
    % 确保输出文件夹存在
    if ~exist(output_folder, 'dir')
        mkdir(output_folder);
    end
    
    % 错误日志文件路径
    log_file = fullfile(output_folder, 'error_log.txt');
    
    % 写入错误信息（包含时间戳）
    fid = fopen(log_file, 'a');
    fprintf(fid, '[%s] 工作表 "%s" 错误: %s\n', datestr(now()), sheet_name, error_msg);
    fclose(fid);
end
