function [avg_imfs, residue] = eemd_function(signal, num_realizations, noise_ratio, interp_method)
    % 输入校验
    signal = signal(:); % 强制列向量
    num_points = length(signal);
    
    % 计算基准噪声
    base_noise = noise_ratio * std(signal);
    
    % 并行分解（直接使用主脚本创建的全局并行池，不重复调用parpool）
    max_imfs = 0;
    all_imfs = cell(num_realizations, 1);
    
    parfor r = 1:num_realizations  % 依赖主脚本的parpool
        % 添加噪声
        current_noise = base_noise * randn(size(signal));
        noisy_signal = signal + current_noise;
        
        % EMD分解
        [imfs, ~] = emd(noisy_signal, 'Interpolation', interp_method);
        
        % 更新最大IMF数
        if ~isempty(imfs)
            current_max = size(imfs, 2);
            if current_max > max_imfs
                max_imfs = current_max;
            end
            all_imfs{r} = imfs;
        end
    end
    
    % 分量对齐与平均（同前）
    avg_imfs = zeros(num_points, max_imfs);
    counts = zeros(1, max_imfs);
    
    for r = 1:num_realizations
        current_imfs = all_imfs{r};
        if isempty(current_imfs)
            continue;
        end
        aligned_imfs = [current_imfs, zeros(num_points, max_imfs - size(current_imfs,2))];
        avg_imfs = avg_imfs + aligned_imfs;
        counts(1:size(current_imfs,2)) = counts(1:size(current_imfs,2)) + 1;
    end
    
    valid_cols = counts > 0;
    avg_imfs = avg_imfs(:, valid_cols) ./ counts(valid_cols);
    residue = signal - sum(avg_imfs, 2);
end