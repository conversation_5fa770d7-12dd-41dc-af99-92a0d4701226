﻿#NoEnv
SetTitleMatchMode, 2  ; 模糊匹配窗口标题

; 检查保存目录是否存在，若不存在则创建
if (!FileExist("D:\ga-lstm\SNRtrend")) {
    FileCreateDir, D:\ga-lstm\SNRtrend
}

; 启动 RTKPLOT
Run, E:\rtklib_2.4.2\rtklib_2.4.2\bin\rtkplot.exe  ; 替换为你的实际路径
WinWaitActive, RTKPLOT,, 10  ; 等待窗口激活（最多10秒）
if ErrorLevel {
    MsgBox, RTKPLOT 启动失败！
    ExitApp
}

; 定义要处理的文件所在目录
fileDir := "D:\ga-lstm\yuanshishuju"

; 遍历目录下的 .o 文件
Loop, %fileDir%\*.21o
{
    oFilePath := A_LoopFileFullPath
    ; 提取文件名（去除路径和扩展名）
    fileName := SubStr(A_LoopFileFullPath, InStr(A_LoopFileFullPath, "\",, -1) + 1)
    saveFileName := SubStr(fileName, 1, InStr(fileName, ".") - 1)

    ; 构建对应的 .n 文件路径
    nFilePath := SubStr(oFilePath, 1, -1) . "n"

    ; 打开 .o 文件
    Send, !f  ; Alt+F 打开文件菜单
    Sleep, 500
    Send, ^o  ; Ctrl+O 打开文件对话框
    Sleep, 1000
    Send, %oFilePath%
    Sleep, 1000
    Send, {Enter}
    Sleep, 1000

    ; 打开 .n 文件
    Send, !f  ; Alt+F 打开文件菜单
    Sleep, 500
    Send, ^n  ; Ctrl+N 打开文件对话框
    Sleep, 1000
    Send, %nFilePath%
    Sleep, 1000
    Send, {Enter}
    Sleep, 2000

    ; 执行保存操作
    Send, !f  ; 打开文件菜单
    Sleep, 500
    ; 导航到保存菜单项（根据实际情况调整）
    Loop, 11 {
        Send, {Down}
        Sleep, 100
    }
    Send, {Enter}
    Sleep, 1000

    ; 构建保存路径（使用提取的文件名）
    savePath := "D:\ga-lstm\SNRtrend\" . saveFileName . ".txt"
    
    ; 输入保存路径
    Send, ^a  ; 全选现有内容
    Sleep, 200
    Send, {Delete}  ; 删除现有内容
    Sleep, 200
    Send, %savePath%
    Sleep, 500
    Send, {Enter}  ; 保存文件
    Sleep, 2000  ; 等待保存完成
}