% 读取 Excel 文件
data = readtable('C:\Users\<USER>\Desktop\SSA-VMD代码\COFA2_snow_cover_plot_1_新版_完整.xlsx');
% 提取数据列（假设第二列为 Var2，第三列为 langgao）
Var2 = data{:, 2};
langgao = data{:, "VMD"};

% 计算残差（第二列减去第三列）
residuals =  Var2-langgao ;

% 绘制散点图
figure('Color', 'white', 'Position', [100 100 800 700]); % 设置图形窗口大小和背景色
scatter(residuals, Var2, 30, , 'filled'); % 橘色散点
% 添加垂直虚线 x = -10 和 x = 10
xline(-10, '--', 'Color', 'k', 'LineWidth', 1.2, 'Label', ''); % 左虚线
xline(10, '--', 'Color', 'k', 'LineWidth', 1.2, 'Label', '');  % 右虚线
% 设置坐标轴标签和标题
xlabel('残差（cm）', 'FontSize', 10);
ylabel('雪深(cm)', 'FontSize', 10);
% 设置横坐标坐标刻度为 -50 到 50
xlim([-50, 50]);
% 设置 x 轴刻度，确保 -10 和 10 显示
xticks([-50:20:50]);
% 调整 ICEEMDAN 到右上角（使用归一化坐标）
text(1, 1, 'VMD', 'Units', 'normalized', 'HorizontalAlignment', 'right', 'VerticalAlignment', 'bottom', 'FontSize', 20);
% 调整坐标轴刻度和布局
box on; % 显示图形边框
set(gca, 'FontSize', 20); % 设置坐标轴字体大小