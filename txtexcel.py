import os
import pandas as pd

folder_path = r'D:\ga-lstm\SNRtrend'

for filename in os.listdir(folder_path):
    if filename.endswith('.txt'):
        input_file = os.path.join(folder_path, filename)
        output_file = os.path.splitext(input_file)[0] + '.xlsx'

        try:
            # 读取 txt 文件（跳过第一行）
            df = pd.read_csv(input_file, sep=r'\s+', header=None, skiprows=1)

            # 创建新的 'TIME' 列：合并第 0 列和第 1 列
            df['TIME'] = df[0].astype(str) + df[1].astype(str)

            # 删除原始的第 0 和 第 1 列
            df = df.drop([0, 1], axis=1)

            # 将 'TIME' 列放回最前面
            cols = ['TIME'] + [col for col in df.columns if col != 'TIME']
            df = df[cols]

            # 设置列名（根据实际数据调整）
            df.columns = ['TIME', 'SAT','AZ','EL', 'SNR', 'MP']

            # 写入 Excel 文件
            df.to_excel(output_file, index=False)

            print(f"已成功将 {filename} 转换为 {os.path.basename(output_file)}")
        except Exception as e:
            print(f"处理 {filename} 时出错: {e}")