clc; clear; close all;
warning('off', 'MATLAB:table:ModifiedVarnames');

%% 添加并行池配置（保持不变）
if isempty(gcp('nocreate'))
    parpool; % 自动检测可用核心数
    % 可手动指定核心数：parpool('local',4)
end

%% 批量处理参数配置（保持不变）
input_folder = 'D:AB332020KECHULI';
output_root = 'D:AB332020CEEMDAN';
min_delta = 1e-9;

%% CEEMDAN特有参数配置（保持不变）
ceemdan_params.num_ensemble = 100;
ceemdan_params.noise_coeff = 0.2;
ceemdan_params.max_imf = 8;

%% 初始化环境（保持不变）
if ~exist(output_root, 'dir')
    mkdir(output_root);
end
excel_files = dir(fullfile(input_folder, '*.xlsx'));

%% 并行处理循环（保持parfor结构）
parfor fileIdx = 1:length(excel_files)
    current_filename = fullfile(input_folder, excel_files(fileIdx).name);
    [~, workbook_name, ~] = fileparts(current_filename);
    output_folder = fullfile(output_root, workbook_name);
    
    fprintf('\n开始处理工作簿: %d/%d | 工作簿名: %s\n', fileIdx, length(excel_files), workbook_name);
    
    if ~exist(output_folder, 'dir')
        mkdir(output_folder);
    end
    [~, sheetList] = xlsfinfo(current_filename);
    results = struct('SheetName', {}, 'MainFreq', {}, 'StartTime', {}, 'EndTime', {});
    
    %% 工作表处理循环（保持不变）
    for sheetIdx = 1:length(sheetList)
        currentSheet = sheetList{sheetIdx};
        fprintf('  处理工作表: %d/%d | 工作表名: %s\n', sheetIdx, length(sheetList), currentSheet);
        
        try 
            %% 数据读取（保持不变）
            opts = detectImportOptions(current_filename, 'Sheet', currentSheet);
            opts.SelectedVariableNames = {'TIME', 'EL_deg', 'SNR_dBHz'};  
            rawData = readtable(current_filename, opts);
            
            %% 数据预处理（保持不变）
            [sinEL, snr_volts, time_valid] = preprocessData(rawData.TIME, rawData.EL_deg, rawData.SNR_dBHz);
            start_time = min(time_valid);
            end_time = max(time_valid);
            
            %% 核心分析（保持不变）
            [zhupin, filtered_signal, sinEL_sorted, imfs, residue] = coreAnalysisCEEMDAN(...
                sinEL, snr_volts, min_delta, ceemdan_params);
            
            %% 结果存储（保持不变）
            results(sheetIdx) = struct( ...
                'SheetName', currentSheet, ...
                'MainFreq', zhupin, ...
                'StartTime', start_time, ...
                'EndTime', end_time ...
            );
            
            %% 生成图表（保持不变）
            if ~isempty(sinEL_sorted) && ~isempty(filtered_signal)
                generateSpectrumPlot(currentSheet, sinEL_sorted, filtered_signal, snr_volts, output_folder);
            end
            if ~isempty(imfs) && ~isempty(residue)
                generateEMDPlot(currentSheet, imfs, residue, sinEL_sorted, output_folder);
            end
            
        catch ME 
            % 简化错误处理（直接输出到命令行）
            fprintf('  错误：工作表 %s 处理失败 - %s\n', currentSheet, ME.message);
            results(sheetIdx) = struct( ...
                'SheetName', currentSheet, ...
                'MainFreq', NaN, ...
                'StartTime', NaT, ...
                'EndTime', NaT ...
            );
        end 
    end 
    
    %% 导出工作簿级汇总表（保持不变）
    exportWorkbookSummary(results, output_folder, workbook_name);
    fprintf('  工作簿处理完成: %s\n', workbook_name);
end 

%% 数据预处理函数（保持不变）
function [sinEL, snr_volts, time_valid] = preprocessData(Time, EL_deg, SNR_dBHz)
    if ~isdatetime(Time)
        try
            Time = datetime(Time, 'ConvertFrom', 'excel');
        catch
            error('时间列格式无法解析');
        end
    end
    validIdx = ~isnat(Time) & ~isnan(EL_deg) & ~isnan(SNR_dBHz);
    if sum(validIdx) < 2
        error('有效数据不足（至少需要2个点）');
    end
    sinEL = sind(EL_deg(validIdx));
    snr_volts = 10.^(SNR_dBHz(validIdx)/20);
    time_valid = Time(validIdx);
    
    % 统一排序处理
    [sinEL, sortIdx] = sort(sinEL);
    snr_volts = snr_volts(sortIdx);
    time_valid = time_valid(sortIdx);
end

%% 核心分析函数（修改为CEEMDAN）
function [zhupin, filtered_signal, sinEL_sorted, imfs, residue] = coreAnalysisCEEMDAN(...
    sinEL, snr_volts, min_delta, ceemdan_params)
    
    zhupin = NaN;
    filtered_signal = [];
    sinEL_sorted = [];
    imfs = [];
    residue = [];
    
    try
        %% 严格递增处理
        sinEL_sorted = makeStrictlyIncreasing(sinEL, min_delta);
        
        %% 验证数据排序
        assert(issorted(sinEL_sorted), 'sinEL必须严格递增排序');
        assert(all(diff(sinEL_sorted) > 0), 'sinEL必须严格递增');
        
        %% CEEMDAN分解原始信号
        [imfs, residue] = custom_ceemdan(snr_volts, ceemdan_params.num_ensemble, ...
            ceemdan_params.noise_coeff, ceemdan_params.max_imf);
        
        %% 筛选有效IMF分量
        if isempty(imfs)
            error('CEEMDAN分解未产生有效IMF分量');
        end
        
        % 计算每个IMF与总和的相关系数
        sum_imfs = sum(imfs, 2);
        correlations = arrayfun(@(i) corr(imfs(:,i), sum_imfs), 1:size(imfs,2));
        valid_imfs = imfs(:, correlations > 0.5);
        
        %% 重构信号
        if isempty(valid_imfs)
            filtered_signal = sum_imfs; % 使用全部IMF
        else
            filtered_signal = sum(valid_imfs, 2);
        end
        
        %% 频谱分析
        f_custom = 0:0.1:400;
        [P, f] = plomb(filtered_signal, sinEL_sorted, f_custom);
        [~, maxIdx] = max(P);
        zhupin = f(maxIdx);
        
    catch ME
        disp(['分析错误: ' ME.message]);
    end
end

%% 严格递增处理函数（保持不变）
function x = makeStrictlyIncreasing(x, minDelta)
    for i = 2:length(x)
        if x(i) <= x(i-1)
            requiredDelta = x(i-1) - x(i) + minDelta;
            x(i) = x(i) + requiredDelta;
        end
    end
end

%% 自定义CEEMDAN实现
function [imfs, residue] = custom_ceemdan(signal, num_ensemble, noise_coeff, max_imf)
    % 参数说明：
    % signal: 输入信号
    % num_ensemble: 集成次数（默认100）
    % noise_coeff: 初始噪声系数（默认0.2）
    % max_imf: 最大IMF数量（默认8）
    
    if nargin < 2, num_ensemble = 100; end
    if nargin < 3, noise_coeff = 0.2; end
    if nargin < 4, max_imf = 8; end
    
    n = length(signal);
    signal_std = std(signal);
    current_residue = signal;
    imfs = zeros(n, max_imf);
    
    for imf_idx = 1:max_imf
        % 计算当前残余信号的标准差
        residue_std = std(current_residue);
        if residue_std == 0, break; end  % 残余信号为常数时停止
        
        % 添加自适应噪声
        noise_std = noise_coeff * residue_std;
        ensemble_imfs = zeros(n, num_ensemble);
        
        for i = 1:num_ensemble
            noisy_signal = current_residue + noise_std * randn(size(current_residue));
            [current_imfs, ~] = emd(noisy_signal, 'Interpolation', 'pchip', 'MaxNumIMF', 1);
            ensemble_imfs(:,i) = current_imfs(:,1);
        end
        
        % 计算集成平均作为当前IMF
        imfs(:,imf_idx) = mean(ensemble_imfs, 2);
        
        % 更新残余信号
        current_residue = current_residue - imfs(:,imf_idx);
    end
    
    % 最终残余信号
    residue = current_residue;
    
    % 去除全零的IMF
    imfs(:,all(imfs==0,1)) = [];
end

%% 频谱图生成函数（保持不变）
function generateSpectrumPlot(sheetName, sinEL_sorted, filtered_signal, original_signal, output_folder)
    fig = figure('Visible', 'off', 'Position', [100 100 900 800]);
    
    % 原始信号子图
    subplot(3,1,1)
    plot(sinEL_sorted, original_signal, 'b', 'LineWidth', 1)
    title(sprintf('%s 原始信号', sheetName), 'Interpreter', 'none')
    xlabel('sin(EL)'), ylabel('Amplitude (V)')
    grid on
    xlim([min(sinEL_sorted) max(sinEL_sorted)])
    
    % 重构信号子图
    subplot(3,1,2)
    plot(sinEL_sorted, filtered_signal, 'r', 'LineWidth', 1)
    title(sprintf('%s 重构信号', sheetName), 'Interpreter', 'none')
    xlabel('sin(EL)'), ylabel('Amplitude (V)')
    grid on
    xlim([min(sinEL_sorted) max(sinEL_sorted)])
    
    % 频谱分析子图
    subplot(3,1,3)
    f_custom = 0:0.1:400;
    [P, f] = plomb(filtered_signal, sinEL_sorted, f_custom);
    plot(f, P, 'k', 'LineWidth', 1)
    xlim([0 400])
    xlabel('Frequency (Hz)')
    ylabel('Power')
    grid on
    
    % 保存图像
    set(findall(fig, 'Type', 'axes'), 'FontSize', 9)
    set(fig, 'Color', 'w')
    saveas(fig, fullfile(output_folder, [sheetName '_频谱分析.png']));
    close(fig);
end

%% EMD可视化函数（保持不变）
function generateEMDPlot(sheetName, imfs, residue, sinEL_sorted, output_folder)
    fig = figure('Visible', 'off', 'Position', [100, 100, 800, 600]);
    numIMFs = size(imfs, 2);
    
    for i = 1:numIMFs
        subplot(numIMFs+1, 1, i);
        plot(sinEL_sorted, imfs(:,i), 'LineWidth', 0.5);
        ylabel(['IMF ', num2str(i)]);
        grid on;
    end
    
    subplot(numIMFs+1, 1, numIMFs+1);
    plot(sinEL_sorted, residue, 'LineWidth', 0.5);
    ylabel('Residue');
    xlabel('sin(EL)');
    grid on;
    
    sgtitle([sheetName ' - CEEMDAN分解结果'], 'Interpreter', 'none');
    saveas(fig, fullfile(output_folder, [sheetName '_CEEMDAN分解.png']));
    close(fig);
end

%% 错误日志函数（保持不变）
function logError(folder, sheet, msg)
    fid = fopen(fullfile(folder, 'error_log.txt'),  'a');
    fprintf(fid, '[%s] %s: %s\n', datestr(now, 'yyyy-mm-dd HH:MM'), sheet, msg);
    fclose(fid);
end

%% 汇总表导出函数（保持不变）
function exportWorkbookSummary(results, folder, workbook_name)
    sheetNames = {results.SheetName}';
    mainFreqs = [results.MainFreq]';
    startTimes = {results.StartTime}';
    endTimes = {results.EndTime}';
    
    T = table(sheetNames, mainFreqs, startTimes, endTimes, ...
        'VariableNames', {'Sheet', 'MainFrequency', 'StartTime', 'EndTime'});
    
    writetable(T, fullfile(folder, sprintf('%s_频率汇总表.csv', workbook_name)));
end