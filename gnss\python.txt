clc; clear; close all;
warning('off', 'MATLAB:table:ModifiedVarnames');

%% 参数配置 
input_folder = 'D:\ga-lstm\p351kechuli';       % 输入文件夹路径
output_folder = 'D:\ga-lstm\p351\p351vmd'; % 结果根目录
min_delta = 1e-9;                      % 严格递增处理参数 

% SSA 参数
pop_size = 20; % 种群数量
max_iter = 20; % 最大迭代次数
dim_k = 1;     % 阶段一维度 (K)
dim_alpha = 1; % 阶段二维度 (alpha)

% 边界设置
lb_k = [3]; ub_k = [8];
lb_alpha = [1000]; ub_alpha = [5000];

% 适应度函数权重
weight_envelope_entropy = 0.5;
weight_energy_loss = 0.5;

%% 获取输入文件夹下所有 Excel 文件
excelFiles = dir(fullfile(input_folder, '*.xlsx'));  
fileNames = {excelFiles.name}';
fullFilePaths = fullfile(input_folder, fileNames); %  Excel 文件完整路径

%% 初始化全局输出目录
if ~exist(output_folder, 'dir')
    mkdir(output_folder);
end

%% 遍历所有 Excel 文件
for fileIdx = 1:length(fullFilePaths)
    filename = fullFilePaths{fileIdx}; % 当前处理的文件
    [~, baseName, ~] = fileparts(filename); % 获取文件名
    % 创建当前工作簿的专属输出文件夹
    current_output_folder = fullfile(output_folder, baseName);
    if ~exist(current_output_folder, 'dir')
        mkdir(current_output_folder);
    end
    
    [~, sheetList] = xlsfinfo(filename); % 获取该文件的所有工作表
    fprintf('\n=== 开始处理文件: %s ===\n', filename);

    [~, sheetList] = xlsfinfo(filename); % 获取该文件的所有工作表
    fprintf('\n=== 开始处理文件: %s ===\n', filename);

    %% 初始化当前文件的结果结构体
    results = struct('SheetName', {}, 'MainFreq', {}, 'Residuals', {}, ...
    'StartTime', {}, 'EndTime', {}, 'OptimalK', {}, 'OptimalAlpha', {}, ...
    'AZ_Mean', {}, 'Residual_Amp', {}, 'Residual_Phase', {});  

    %% 主处理循环，逐个处理工作表
    for sheetIdx = 1:length(sheetList)
        currentSheet = sheetList{sheetIdx};
        try 
            fprintf('\n开始处理工作表: %s\n', currentSheet);

            % 数据读取 (包含时间列)
            opts = detectImportOptions(filename, 'Sheet', currentSheet);
            opts.SelectedVariableNames = {'TIME', 'EL', 'SNR'};  
            rawData = readtable(filename, opts);

            % 数据预处理 
            [sinEL, snr_volts, time_valid] = preprocessData(rawData.TIME, rawData.EL, rawData.SNR);
            start_time = min(time_valid);
            end_time = max(time_valid);

            %% 第一阶段：优化 K，固定 alpha = 2000
            k_candidates = 3:8;
            fitness_k = zeros(size(k_candidates));

            for idx = 1:length(k_candidates)
                k_test = k_candidates(idx);
                f = objective_function(k_test, sinEL, snr_volts, min_delta, ...
                    weight_envelope_entropy, weight_energy_loss, 1); % phase=1
                fitness_k(idx) = f;
                fprintf('K=%d, Fitness=%.6f\n', k_test, f);
            end

            [~, best_idx] = min(fitness_k);
            best_k = k_candidates(best_idx);
            best_fitness_k = fitness_k(best_idx);

            fprintf('阶段一最佳 K 值: %d, 适应度: %.6f\n', best_k, best_fitness_k);

            %% 第二阶段：固定 K，优化 alpha
            best_alpha = refine_alpha_search(sinEL, snr_volts, best_k, min_delta, ...
                weight_envelope_entropy, weight_energy_loss);

            fprintf('最终参数 - K=%d, alpha=%d\n', best_k, best_alpha);

            %% 核心分析 (使用优化后的 VMD 参数)
            [~, ~, sinEL_unsorted, snr_volts_unsorted, imf_unsorted] = coreAnalysis(sinEL, snr_volts, best_k, best_alpha, min_delta, false);

            % 同步排序所有数据（仅一次）
            [sinEL_sorted, sortIdx] = sort(sinEL_unsorted); 
            snr_volts_sorted = snr_volts_unsorted(sortIdx);
            imf_sorted = imf_unsorted(sortIdx, :);
            
            % 严格递增处理（仅调整值，不改变顺序）
            sinEL_sorted = makeStrictlyIncreasing(sinEL_sorted, min_delta);
            
            %% 最终分析（基于同步后的数据）
            fprintf('\n=== 最终VMD分解结果分析 ===\n');
            
            % 计算初始残差序列
            residual1 = snr_volts_sorted - imf_sorted(:, end);
            
            % 与其他IMF分量进行相关性分析
            corr_threshold = 0.3;
            selected_imfs = [];
            
            % 遍历排序后的IMF分量
            for i = 1:size(imf_sorted, 2)-1
                corr_coef = corr(residual1, imf_sorted(:, i));
                fprintf('IMF%d与残差的相关系数: %.3f %s\n', i, corr_coef, ...
                    ifelse(abs(corr_coef)>corr_threshold,'(保留)','(丢弃)'));
                if abs(corr_coef) > corr_threshold
                    selected_imfs = [selected_imfs, imf_sorted(:, i)];
                end
            end
            
            % 计算最终残差序列
            if ~isempty(selected_imfs)
                residuals = sum(selected_imfs, 2);
            else
                residuals = zeros(size(residual1));
            end
            
            % Lomb-Scargle分析（使用同步后的数据）
            f_custom = 0:0.1:400;
            [P, f] = plomb(residuals, sinEL_sorted, f_custom);
            
            % 提取主频
            [~, maxIdx] = max(P);
            zhupin = f(maxIdx);
            
            fprintf('最终主频: %.2f Hz\n', zhupin);

            % 计算AZ均值
            opts = detectImportOptions(filename, 'Sheet', currentSheet);
            if any(strcmp(opts.VariableNames, 'AZ'))
                opts.SelectedVariableNames = {'AZ'};
                azData = readtable(filename, opts);
                az_mean = mean([azData.AZ(1), azData.AZ(end)]);
            else
                az_mean = NaN;
            end

            % 非线性拟合残差序列
            if ~isempty(residuals)
                % 使用找到的主频作为已知频率
                f0 = zhupin; 
                xdata = sinEL_sorted;
                ydata = residuals;
                
                % 定义正弦拟合模型
                model = @(p,x) p(1)*sin(2*pi*f0*x + p(2));
                
                % 初始参数估计
                A0 = max(abs(ydata));
                phi0 = 0;
                
                % 非线性最小二乘拟合
                try
                    params = lsqcurvefit(model, [A0; phi0], xdata, ydata);
                    amp = params(1);
                    phase = params(2);
                catch
                    amp = NaN;
                    phase = NaN;
                end
            else
                amp = NaN;
                phase = NaN;
            end

            % 结果存储 
            results(sheetIdx).SheetName = currentSheet;
            results(sheetIdx).MainFreq = zhupin;
            if ~isempty(sinEL_sorted) && ~isempty(residuals)
                results(sheetIdx).Residuals = [sinEL_sorted(:), residuals(:)];
            else
                results(sheetIdx).Residuals = [];
            end
            results(sheetIdx).StartTime = start_time;
            results(sheetIdx).EndTime = end_time;
            results(sheetIdx).OptimalK = best_k;
            results(sheetIdx).OptimalAlpha = best_alpha;
            results(sheetIdx).AZ_Mean = az_mean;            
            results(sheetIdx).Residual_Amp = amp;           
            results(sheetIdx).Residual_Phase = phase;   

            % 生成频谱图 
            if ~isempty(sinEL_sorted) && ~isempty(residuals)
                generateSpectrumPlot(currentSheet, sinEL_sorted, residuals, output_folder);
            end
            saveVMDDecompositionPlot(currentSheet, sinEL_sorted, snr_volts_sorted, imf_sorted, current_output_folder);
            % 保存 VMD 分解效果图（使用排序后的IMF）
            saveVMDDecompositionPlot(currentSheet, sinEL_sorted, snr_volts_sorted, imf_sorted, output_folder);

        catch ME 
            logError(output_folder, currentSheet, ME.message); 
            continue
        end 
    end 

    %% 结果导出（按文件）
    exportSummary(results, current_output_folder);
end
fprintf('\n==== 所有文件分析完成 ====\n');
%% 目标函数
function fitness = objective_function(x, sinEL, snr_volts, min_delta, ...
    weight_envelope_entropy, weight_energy_loss, phase, fixed_K)

    if nargin < 7
        phase = 1; % 默认为第一阶段
    end
    
    if nargin < 8
        fixed_K = []; % 如果未传入 fixed_K，则不固定 K
    end

    switch phase
        case 1 % 阶段一：只优化 K，alpha 固定
            num_imfs = round(x(1)); % x(1) 表示 K
            alpha = 2000; % 固定 alpha
        case 2 % 阶段二：固定 K，优化 alpha
            num_imfs = fixed_K;
            alpha = x(1); % x(1) 表示 alpha
    end

    % VMD分解
    [~, ~, ~, ~, imf] = coreAnalysis(sinEL, snr_volts, num_imfs, alpha, min_delta, false);
    
    % 计算适应度（基于IMF的质量）
    envelope_entropy_value = envelope_entropy(imf(:, 1));
    reconstructed_signal = sum(imf, 2);
    energy_loss_value = norm(snr_volts(:) - reconstructed_signal) / norm(snr_volts(:));
    
    fitness = weight_envelope_entropy * envelope_entropy_value + ...
              weight_energy_loss * energy_loss_value;
end

%% 预处理函数 
function [sinEL, snr_volts, time_valid] = preprocessData(Time, EL, SNR)
    % 转换时间格式并过滤无效数据
    if ~isdatetime(Time)
        try
            Time = datetime(Time, 'ConvertFrom', 'excel');
        catch
            error('时间列格式无法解析');
        end
    end
    validIdx = ~isnat(Time) & ~isnan(EL) & ~isnan(SNR);
    if sum(validIdx) < 2
        error('有效数据不足（至少需要2个点）');
    end
    sinEL = sind(EL(validIdx));
    snr_volts = 10.^(SNR(validIdx)/20);
    time_valid = Time(validIdx);
end

%% 核心分析函数（修改后）
function [zhupin, residuals, sinEL_sorted, snr_volts_sorted, imf] = coreAnalysis(sinEL, snr_volts, num_imfs, alpha, min_delta, verbose)
    if nargin < 6
        verbose = false; % 默认不显示输出，避免重复输出
    end
    
    zhupin = NaN;
    residuals = [];
    sinEL_sorted = [];
    snr_volts_sorted = [];
    
    try
        if isempty(sinEL) || isempty(snr_volts) || length(sinEL) ~= length(snr_volts)
            error('输入数据不合法');
        end
        
        % VMD分解
        [imf, ~] = vmd(snr_volts(:), 'NumIMFs', num_imfs, 'PenaltyFactor', alpha);
        
        % 只返回imf，不进行相关性分析和残差计算（在主程序中处理）
        sinEL_sorted = sinEL;
        snr_volts_sorted = snr_volts;
        
    catch ME
        error('分析失败: %s', ME.message);
    end
end

%% 可视化函数 
function generateSpectrumPlot(sheetName, sinEL_sorted, residuals, output_folder)
    fig = figure('Visible', 'off');
    subplot(2,1,1)
    plot(sinEL_sorted, residuals, "red")
    title(sprintf('%s 残差分布', sheetName), 'Interpreter', 'none')
    xlabel('sin(EL)'), ylabel('Residual (V)')
    subplot(2,1,2)
    f_custom = 0:0.1:400;
    [P, f] = plomb(residuals, sinEL_sorted,f_custom);
    xlim([0 400])
    plot(f, P)
    xlabel('Frequency (Hz)')
    ylabel('Power')
    grid on 
    saveas(fig, fullfile(output_folder, sprintf('%s_频谱.png', sheetName)));
    close(fig);
end

%% 辅助函数 
function logError(folder, sheet, msg)
    fid = fopen(fullfile(folder, 'error_log.txt'),  'a');
    fprintf(fid, '[%s] %s: %s\n', datestr(now, 'yyyy-mm-dd HH:MM'), sheet, msg);
    fclose(fid);
end 

%% 修改后的导出汇总表函数 (带错误处理)
function exportSummary(results, folder)
    try
        % 检查结果是否为空
        if isempty(results)
            fprintf('警告: 结果为空，跳过导出\n');
            return;
        end
        
        % 确保所有字段都存在且长度一致
        sheetNames = {results.SheetName}';
        mainFreqs = [results.MainFreq]';
        startTimes = {results.StartTime}';
        endTimes = {results.EndTime}';
        optimalKs = [results.OptimalK]';
        optimalAlphas = [results.OptimalAlpha]';
        
        % 处理可能缺失的字段
        if isfield(results, 'AZ_Mean')
            azMeans = [results.AZ_Mean]';
        else
            azMeans = NaN(size(sheetNames));
        end
        
        if isfield(results, 'Residual_Amp')
            residualAmps = [results.Residual_Amp]';
        else
            residualAmps = NaN(size(sheetNames));
        end
        
        if isfield(results, 'Residual_Phase')
            residualPhases = [results.Residual_Phase]';
        else
            residualPhases = NaN(size(sheetNames));
        end
        
        % 验证所有变量长度相同
        numRows = length(sheetNames);
        if ~all([length(mainFreqs), length(startTimes), length(endTimes), ...
                length(optimalKs), length(optimalAlphas), length(azMeans), ...
                length(residualAmps), length(residualPhases)] == numRows)
            error('结果字段长度不一致');
        end
        
        % 创建表格
        T = table(sheetNames, mainFreqs, startTimes, endTimes, optimalKs, optimalAlphas, ...
            azMeans, residualAmps, residualPhases, ...
            'VariableNames', {'Sheet', 'MainFrequency', 'StartTime', 'EndTime', ...
            'OptimalK', 'OptimalAlpha', 'AZ_Mean', 'Residual_Amplitude', 'Residual_Phase'});
        
        % 导出
        writetable(T, fullfile(folder, '频率汇总表.csv'));
        save(fullfile(folder, '完整结果.mat'), 'results');
        
    catch ME
        % 记录错误但继续执行
        fprintf('导出结果时发生错误: %s\n跳过当前文件\n', ME.message);
        logError(folder, 'exportSummary', ME.message);
    end
end
%% 严格递增处理函数
function x = makeStrictlyIncreasing(x, minDelta)
    for i = 2:length(x)
        if x(i) <= x(i-1)
            requiredDelta = x(i-1) - x(i) + minDelta;
            x(i) = x(i) + requiredDelta;
        end
    end
end

%% 保存 VMD 分解效果图函数
function saveVMDDecompositionPlot(sheetName, sinEL_sorted, snr_volts_sorted, imf, output_folder)
    num_imfs = size(imf, 2);
    fig = figure('Visible', 'off');
    
    % 绘制原始数据子图
    subplot(num_imfs + 1, 1, 1);
    plot(sinEL_sorted, snr_volts_sorted,"red");
    title(sprintf('%s - 原始数据', sheetName));
    ylabel('幅值');
    
    % 绘制 IMF 子图
    for i = 1:num_imfs
        subplot(num_imfs + 1, 1, i + 1);
        plot(sinEL_sorted, imf(:, i));
        title(sprintf('IMF %d',  i));
        ylabel('幅值');
    end
    
    saveas(fig, fullfile(output_folder, sprintf('%s_VMD分解.png', sheetName)));
    close(fig);
end

%% 计算包络熵函数
function entropy = envelope_entropy(signal)
    % 计算信号的包络
    env = abs(hilbert(signal));
    
    % 归一化包络
    norm_env = env / sum(env);
    
    % 计算包络熵
    entropy = -sum(norm_env .* log2(norm_env + eps));
end

%% 计算能量损失函数
function energy_loss = calculate_energy_loss(original_signal, imf)
    total_energy_imf = sum(sum(imf.^2));
    total_energy_original = sum(original_signal.^2);
    energy_loss = abs(total_energy_original - total_energy_imf);
end

%% 使用三分法优化 alpha 搜索（修改后）
function best_alpha = refine_alpha_search(sinEL, snr_volts, best_k, min_delta, ...
    weight_envelope_entropy, weight_energy_loss)

    % 初始候选点
    candidates = [10, 1000, 2000, 3000, 4000, 5000];
    fitness_alpha = zeros(size(candidates));
    
    % 计算初始候选点的适应度
    for i = 1:length(candidates)
        alpha = candidates(i);
        [~, ~, ~, ~, imf] = coreAnalysis(sinEL, snr_volts, best_k, alpha, min_delta, false);
        
        % 计算适应度（基于IMF的质量）
        envelope_entropy_value = envelope_entropy(imf(:, 1));
        reconstructed_signal = sum(imf, 2);
        energy_loss_value = norm(snr_volts(:) - reconstructed_signal) / norm(snr_volts(:));
        
        fitness_alpha(i) = weight_envelope_entropy * envelope_entropy_value + ...
                          weight_energy_loss * energy_loss_value;
                          
        fprintf('初始候选 alpha=%5d, Fitness=%.6f\n', alpha, fitness_alpha(i));
    end
    
    % 找出最佳区间
    [~, best_idx] = min(fitness_alpha);
    
    % 确定搜索区间
    if best_idx == 1
        a = 10;
        b = 1000;
    elseif best_idx == length(candidates)
        a = 4000;
        b = 5000;
    else
        a = candidates(best_idx-1);
        b = candidates(best_idx+1);
    end
    
    fprintf('\n=== 开始三分法搜索区间 [%d, %d] ===\n', a, b);
    
    % 三分法参数
    max_iter = 10;       % 最大迭代次数
    tol = 10;            % 容忍度（alpha整数）
    golden_ratio = 0.618; % 黄金分割比例
    
    % 初始化三个点
    x1 = a;
    x3 = b;
    x2 = round(a + golden_ratio*(b - a));
    
    % 计算初始点的适应度
    f1 = computeFitness(x1, sinEL, snr_volts, best_k, min_delta, weight_envelope_entropy, weight_energy_loss);
    f2 = computeFitness(x2, sinEL, snr_volts, best_k, min_delta, weight_envelope_entropy, weight_energy_loss);
    f3 = computeFitness(x3, sinEL, snr_volts, best_k, min_delta, weight_envelope_entropy, weight_energy_loss);
    
    iter = 0;
    while (b - a) > tol && iter < max_iter
        iter = iter + 1;
        fprintf('迭代 %d: [%d, %d, %d] 适应度: [%.4f, %.4f, %.4f]\n', ...
                iter, x1, x2, x3, f1, f2, f3);
        
        % 确定最小值所在区间
        if f2 < f1 && f2 < f3
            % 最小值在中间区间
            best_alpha = x2;
            break;
        elseif f1 < f3
            % 最小值在左侧区间
            b = x2;
            x3 = x2;
            x2 = round(a + golden_ratio*(b - a));
            f3 = f2;
            f2 = computeFitness(x2, sinEL, snr_volts, best_k, min_delta, weight_envelope_entropy, weight_energy_loss);
        else
            % 最小值在右侧区间
            a = x2;
            x1 = x2;
            x2 = round(a + golden_ratio*(b - a));
            f1 = f2;
            f2 = computeFitness(x2, sinEL, snr_volts, best_k, min_delta, weight_envelope_entropy, weight_energy_loss);
        end
    end
    
    % 最终检查三个点
    fitness_values = [f1, f2, f3];
    [min_fitness, idx] = min(fitness_values);
    points = [x1, x2, x3];
    best_alpha = points(idx);
    
    % 在最优值附近做最后检查
    candidates_final = unique([best_alpha-5, best_alpha, best_alpha+5]);
    fitness_final = zeros(size(candidates_final));
    for i = 1:length(candidates_final)
        fitness_final(i) = computeFitness(candidates_final(i), sinEL, snr_volts, best_k, min_delta, weight_envelope_entropy, weight_energy_loss);
    end
    [min_fitness_final, idx] = min(fitness_final);
    best_alpha = candidates_final(idx);
    
    fprintf('=== 三分法搜索完成 ===\n');
    fprintf('最佳 alpha=%d, 适应度=%.6f\n\n', best_alpha, min_fitness_final);
end

%% 计算适应度的辅助函数
function fitness = computeFitness(alpha, sinEL, snr_volts, k, min_delta, w_entropy, w_loss)
    [~, ~, ~, ~, imf] = coreAnalysis(sinEL, snr_volts, k, alpha, min_delta, false);
    entropy = envelope_entropy(imf(:, 1));
    reconstructed_signal = sum(imf, 2);
    loss = norm(snr_volts(:) - reconstructed_signal) / norm(snr_volts(:));
    fitness = w_entropy * entropy + w_loss * loss;
end

function result = ifelse(condition, trueval, falseval)
    if condition
        result = trueval;
    else
        result = falseval;
    end
end