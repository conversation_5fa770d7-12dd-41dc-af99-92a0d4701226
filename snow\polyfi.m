clc; clear; close all;
warning('off', 'MATLAB:table:ModifiedVarnames');

%% 批量处理参数配置
input_folder = 'D:\excelsnow\P3512021KECHULI';   % 输入文件夹路径
output_root = 'D:\excelsnow\P3512021PLOY';       % 结果根目录
poly_degree = 2;                                 % 多项式拟合阶数
min_delta = 1e-9;                                % 严格递增处理参数

%% 初始化环境
if ~exist(output_root, 'dir')
    mkdir(output_root);
end

% 获取输入文件夹下所有xlsx文件
excel_files = dir(fullfile(input_folder, '*.xlsx'));

%% 批量处理循环
for fileIdx = 1:length(excel_files)
    current_filename = fullfile(input_folder, excel_files(fileIdx).name);
    % 提取工作簿名称（不带扩展名）
    [~, workbook_name, ~] = fileparts(current_filename);
    output_folder = fullfile(output_root, workbook_name); % 以工作簿名创建子文件夹
    
    fprintf('\n开始处理工作簿: %d/%d | 工作簿名: %s\n', fileIdx, length(excel_files), workbook_name);
    
    %% 初始化工作簿级结果存储
    if ~exist(output_folder, 'dir')
        mkdir(output_folder);
    end
    [~, sheetList] = xlsfinfo(current_filename);
    results = struct('SheetName', {}, 'MainFreq', {}, 'StartTime', {}, 'EndTime', {}); % 存储所有工作表结果
    
    %% 工作表处理循环
    for sheetIdx = 1:length(sheetList)
        currentSheet = sheetList{sheetIdx};
        fprintf('  处理工作表: %d/%d | 工作表名: %s\n', sheetIdx, length(sheetList), currentSheet);
        
        try 
            % 数据读取
            opts = detectImportOptions(current_filename, 'Sheet', currentSheet);
            opts.SelectedVariableNames = {'TIME', 'EL_deg', 'SNR_dBHz'};  
            rawData = readtable(current_filename, opts);
            
            % 数据预处理
            [sinEL, snr_volts, time_valid] = preprocessData(rawData.TIME, rawData.EL_deg, rawData.SNR_dBHz);
            start_time = min(time_valid);
            end_time = max(time_valid);
            
            % 核心分析
            [zhupin, residuals, sinEL_sorted] = coreAnalysis(sinEL, snr_volts, poly_degree, min_delta);
            
            % 结果存储（工作簿级结果集合）
            results(sheetIdx) = struct( ...
                'SheetName', currentSheet, ...
                'MainFreq', zhupin, ...
                'StartTime', start_time, ...
                'EndTime', end_time ...
            );
            
            % 生成图表（保存到工作簿对应的文件夹，命名包含工作表名）
            if ~isempty(sinEL_sorted) && ~isempty(residuals)
                generateSpectrumPlot(currentSheet, sinEL_sorted, residuals, output_folder);
            end
            
        catch ME 
            logError(output_folder, currentSheet, ME.message); 
            % 记录错误但继续处理下一个工作表
            results(sheetIdx) = struct( ...
                'SheetName', currentSheet, ...
                'MainFreq', NaN, ...
                'StartTime', NaT, ...
                'EndTime', NaT ...
            );
        end 
    end 
    
    %% 导出工作簿级汇总表（以工作簿名命名）
    exportWorkbookSummary(results, output_folder, workbook_name);
    fprintf('  工作簿处理完成: %s\n', workbook_name);
end 

%% 预处理函数（保持不变）
function [sinEL, snr_volts, time_valid] = preprocessData(Time, EL_deg, SNR_dBHz)
    if ~isdatetime(Time)
        try
            Time = datetime(Time, 'ConvertFrom', 'excel');
        catch
            error('时间列格式无法解析');
        end
    end
    validIdx = ~isnat(Time) & ~isnan(EL_deg) & ~isnan(SNR_dBHz);
    if sum(validIdx) < 2
        error('有效数据不足（至少需要2个点）');
    end
    sinEL = sind(EL_deg(validIdx));
    snr_volts = 10.^(SNR_dBHz(validIdx)/20);
    time_valid = Time(validIdx);
end

%% 核心分析函数（保持不变）
function [zhupin, residuals, sinEL_sorted] = coreAnalysis(sinEL, snr_volts, poly_degree, min_delta)
    zhupin = NaN;
    residuals = [];
    sinEL_sorted = [];
    try
        if isempty(sinEL) || isempty(snr_volts) || length(sinEL) ~= length(snr_volts)
            error('输入数据不合法');
        end
        
        coeffs = polyfit(sinEL, snr_volts, poly_degree);
        residuals = snr_volts - polyval(coeffs, sinEL);
        [sinEL_sorted, sortIdx] = sort(sinEL);
        sinEL_sorted = makeStrictlyIncreasing(sinEL_sorted, min_delta);
        residuals = residuals(sortIdx);
        
        f_custom = 0:0.1:400;
        [P, f] = plomb(residuals, sinEL_sorted, f_custom);
        [~, maxIdx] = max(P);
        zhupin = f(maxIdx);
        
    catch
        % 错误处理
    end
end

%% 可视化函数（路径调整为工作簿文件夹，图表名包含工作表名）
function generateSpectrumPlot(sheetName, sinEL_sorted, residuals, output_folder)
    fig = figure('Visible', 'off');
    subplot(2,1,1)
    scatter(sinEL_sorted, residuals, 15, 'filled')
    title(sprintf('%s 残差分布', sheetName), 'Interpreter', 'none')
    xlabel('sin(EL)'), ylabel('Residual (V)')
    
    subplot(2,1,2)
    f_custom = 0:0.1:400;
    [P, f] = plomb(residuals, sinEL_sorted,f_custom);
    xlim([0 400])
    plot(f, P)
    xlabel('Frequency (Hz)')
    ylabel('Power')
    grid on 
    % 保存到工作簿文件夹，文件名包含工作表名
    saveas(fig, fullfile(output_folder, sprintf('%s_频谱.png', sheetName)));
    close(fig);
end

%% 辅助函数（错误日志保存到工作簿文件夹）
function logError(folder, sheet, msg)
    fid = fopen(fullfile(folder, 'error_log.txt'),  'a');
    fprintf(fid, '[%s] %s: %s\n', datestr(now, 'yyyy-mm-dd HH:MM'), sheet, msg);
    fclose(fid);
end 

%% 工作簿级汇总表导出函数
function exportWorkbookSummary(results, folder, workbook_name)
    % 整理数据
    sheetNames = {results.SheetName}';
    mainFreqs = [results.MainFreq]';
    startTimes = {results.StartTime}';
    endTimes = {results.EndTime}';
    
    % 创建表格
    T = table(sheetNames, mainFreqs, startTimes, endTimes, ...
        'VariableNames', {'Sheet', 'MainFrequency', 'StartTime', 'EndTime'});
    
    % 保存为工作簿名命名的汇总表
    writetable(T, fullfile(folder, sprintf('%s_频率汇总表.csv', workbook_name)));
    % 可选：保存完整结果mat文件
    % save(fullfile(folder, sprintf('%s_完整结果.mat', workbook_name)), 'results');
end

%% 严格递增处理函数（保持不变）
function x = makeStrictlyIncreasing(x, minDelta)
    for i = 2:length(x)
        if x(i) <= x(i-1)
            requiredDelta = x(i-1) - x(i) + minDelta;
            x(i) = x(i) + requiredDelta;
        end
    end
end