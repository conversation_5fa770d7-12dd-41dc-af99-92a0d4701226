% ===== 配置参数 =====
input_folder = 'C:\Users\<USER>\Desktop\snow\tu';  % 输入文件夹路径

% 获取所有Excel文件（包含.xls和.xlsx）
file_list = dir(fullfile(input_folder, '*.xls*'));

% 处理所有Excel文件
for file_idx = 1:numel(file_list)
    try
        input_file = fullfile(file_list(file_idx).folder, file_list(file_idx).name);
        [~, filename, ~] = fileparts(input_file);
        fprintf('处理文件: %s (%d/%d)\n', filename, file_idx, numel(file_list));

        % ===== 批量处理所有工作表 =====
        try
            [~, sheets] = xlsfinfo(input_file);
        catch ME
            warning('无法读取文件 %s: %s', input_file, ME.message);
            continue;
        end

        for sheet_idx = 1:numel(sheets)
            sheet_name = sheets{sheet_idx};
            fprintf('  处理工作表: %s (%d/%d)\n', sheet_name, sheet_idx, numel(sheets));

            % 读取当前工作表数据（保持原有逻辑）
            try
                data = readtable(input_file, 'Sheet', sheet_name);
            catch ME
                warning('    跳过工作表 %s: %s', sheet_name, ME.message);
                continue;
            end

            % 尝试处理工作表，如果出错则跳过
            try
                % ===== 数据预处理（保持原有逻辑不变）=====
                TIME = data.TIME;
                SNR = data.SNR_dBHz;
                EL = data.EL_deg;

                % 强制转换时间列为数值型（datenum格式）
                if ~isnumeric(TIME)
                    try
                        TIME = datenum(TIME);  % 自动尝试转换
                    catch
                        TIME = datenum(datetime(TIME, 'InputFormat', 'yyyy-MM-dd HH:mm:ss'));
                    end
                end

                % 计算sinEL并转换为弧度
                sinEL = sind(EL);        % 直接计算度数正弦值
                % 对sinEL进行递增排序，并同步调整TIME和SNR
                [sinEL_sorted, idx] = sort(sinEL);
                TIME_sorted = TIME(idx);
                SNR_sorted_1 = SNR(idx);
                SNR_sorted = 10.^(SNR_sorted_1/20);
                % 确保sinEL严格递增（保持原始数据趋势）
                minDelta = 1e-6;  % 最小增量，避免数值问题
                sinEL_strict = makeStrictlyIncreasing(sinEL_sorted, minDelta);

                % 标准化处理后的SNR
                SNR = SNR_sorted;  % 使用排序后的SNR
                SNR = SNR(:);           % 确保为列向量
                desvio_x = std(SNR);   % 计算原始信号标准差
                SNR = SNR / desvio_x;  % 标准化

                % ===== 参数设置 =====
                Nstd = 0.2;     % 噪声强度系数（典型值0.1-0.4）
                NR = 100;       % 噪声实现次数（典型值50-200）
                MaxIter = 1000; % 最大迭代次数
                SNRFlag = 2;    % 噪声标准化标志（1=不标准化，2=标准化）

                % ===== 初始化变量 =====
                x = SNR;
                num_samples = length(x);
                modes = zeros(num_samples, 0);  % 存储IMF的列矩阵
                residue = x;                    % 初始残差
                aux = zeros(num_samples, 1);    % 辅助变量
                iter = zeros(NR, round(log2(num_samples) + 5));

                % ===== 生成高斯白噪声 =====
                white_noise = cell(NR, 1);
                for i = 1:NR
                    white_noise{i} = randn(num_samples, 1);  % 列向量噪声
                end

                % ===== 预分解噪声的IMF =====
                noise_modes = cell(NR, 1);
                for i = 1:NR
                    noise_modes{i} = emd(white_noise{i});  % 每个噪声的IMF矩阵
                end

                % ===== 主分解循环 =====
                k = 1;
                while true
                    % 生成带噪信号
                    if k == 1
                        % 第一个IMF的特殊处理
                        for i = 1:NR
                            % 获取噪声的第一个IMF并标准化
                            imf_noise = noise_modes{i}(1, :)';  % 转置为列
                            imf_noise = imf_noise / std(imf_noise);

                            % 构造带噪信号
                            noisy_signal = residue + Nstd * imf_noise;

                            % EMD分解
                            [temp, ~, it] = emd(noisy_signal, 'MAXMODES', 1, 'MAXITERATIONS', MaxIter);
                            imf = temp(1, :)';  % 提取第一个IMF并转置

                            % 更新辅助变量
                            aux = aux + (noisy_signal - imf) / NR;
                            iter(i, k) = it;
                        end
                    else
                        % 后续IMF的通用处理
                        for i = 1:NR
                            % 获取对应阶数的噪声IMF
                            if size(noise_modes{i}, 1) >= k
                                imf_noise = noise_modes{i}(k, :)';  % 转置为列
                            else
                                imf_noise = zeros(num_samples, 1);  % 不足时补零
                            end

                            % 调整噪声强度
                            if SNRFlag == 2
                                imf_noise = imf_noise / std(imf_noise);
                            end
                            imf_noise = Nstd * imf_noise;

                            % 构造带噪信号
                            noisy_signal = residue + std(residue) * imf_noise;

                            % ICEEMDAN分解（这里是主要修改点）
                            [temp, ~ ] = emd(noisy_signal + 0.2 * imf_noise, 'MAXMODES', 1, 'MAXITERATIONS', MaxIter);
                            imf = temp(end, :)';  % 提取最后一个IMF并转置

                            % 更新辅助变量
                            aux = aux + imf / NR;
                            iter(i, k) = it;
                        end
                    end

                    % 保存当前IMF
                    current_imf = residue - aux;
                    modes = [modes, current_imf];  % 列追加模式

                    % 更新残差
                    residue = aux;
                    aux = zeros(num_samples, 1);  % 重置辅助变量

                    % 终止条件检查
                    if size(emd(residue, 'MAXMODES', 1, 'MAXITERATIONS', MaxIter), 1) <= 1
                        break;
                    end
                    k = k + 1;
                end

                % 添加最终残差
                modes = [modes, residue];

                % ===== 后处理 =====
                modes = modes * desvio_x;  % 恢复原始尺度

                % ===== 生成ICEEMDAN分解图 =====
%                 fig_decomposition = figure('Visible', 'on', 'Color', 'w');
% 
%                 % 绘制原始信号
%                 subplot(size(modes,2)+1, 1, 1);
% 
% %                 ylim([30,100])
%               plot(sinEL_strict, x*desvio_x, 'Color', 'r', 'LineWidth', 1.2);
%                  xlim([0.05,0.45])
%                 ylabel('SNR','FontSize',12);
% %                 title(sprintf('ICEEMDAN分解结果 - %s', sheet_name));
%                 grid on;
%                 set(gca, 'XTickLabel', []); % 不显示当前子图的横坐标刻度
                % 绘制所有IMF分量
% % 绘制所有IMF分量
% for i = 1:size(modes,2)-1
%     subplot(size(modes,2)+1, 1, i+1);
%     % 根据IMF编号设置颜色
%     if i == 1 || i == 2
%         line_color = 'black'; % 绿色
%     elseif i >= 3 && i <= 5
%         line_color = 'black'; % 蓝色
%     elseif i == 6
%         line_color = 'black'; % 洋红
%     else
%         line_color = 'black'; % 其他情况使用黑色
%     end
%     plot(sinEL_strict, modes(:,i), line_color, 'LineWidth', 1.2);
%     ylabel(['IMF ', num2str(i)],'FontSize',12);
%     xlim([0.05,0.45])
%     set(gca, 'XTickLabel', []); % 不显示当前子图的横坐标刻度
%     grid on;
% end
%  
%                 % 绘制残差
%                 subplot(size(modes,2)+1, 1, size(modes,2)+1);
%                 plot(sinEL_strict, modes(:,end), 'g', 'LineWidth', 1.2);
%                  xlim([0.05,0.45])
%                 ylabel('RESIDUAL','FontSize',12);
%                 xlabel('sinEL');
%                 grid on;
% 
%                 % 调整子图间距
%                 set(fig_decomposition, 'Position', [100, 10, 900, 600]);
% 
% %                 % ===== 频谱分析并显示图像 =====
% %                 imf_sum = sum(modes(:, 1:end-1), 2);  % 所有IMF的和（排除残差）
% %                 correlations = zeros(1, size(modes, 2)-1);
% 
%                 % 计算每个IMF与总和的相关系数
%                 for i = 1:size(modes, 2)-1
%                     correlations(i) = corr(modes(:, i), imf_sum);
%                 end
%                 % 筛选相关系数>0.5的IMF并合并
%                 selected_imfs = modes(:, correlations > 0.35);
%                 first_component = sum(selected_imfs, 2);
%                 % 使用自定义频率范围进行频谱分析
%                 f_custom = 0:0.1:100;
%                 [P_first, f_first] = plomb(first_component, sinEL_strict, f_custom);
%                 [~, maxIdx_first] = max(P_first)
%                 zhupin_first = f_first(maxIdx_first);  % 合并信号主频
%                 % 创建专用图形对象
% %                 fig = figure('Visible', 'on', 'Color', 'w');  % 保持可见模式
% 
% %                 % 绘制频谱图（上方）
% %                 subplot(2,1,1);
% %                 plot(f_first, P_first);
% %                 xlabel('频率 (Hz)');
% %                 ylabel('功率');
% %                 title(sprintf('合并信号频谱 (主频: %.2f Hz)', zhupin_first));
% %                 xlim([f_custom(1), f_custom(end)]);
% %                 grid on;
% % 
% %                 % 绘制合成信号（下方）
% %                 subplot(2,1,2);
% %                 plot(sinEL_strict, first_component);
% %                 xlabel('sinEL');
% %                 ylabel('幅度');
% %                 title('合成信号（IMF合并结果）');
% %                 xlim([min(sinEL_strict), max(sinEL_strict)]);
% %                 grid on;
% 
%                 % 强制刷新图形
%                 drawnow;
figure('Color', 'white', 'Position', [100 100 1000 200]); % 设置图形窗口大小和背景色
% 使用二次多项式进行拟合
p = polyfit(sinEL_strict, x*desvio_x, 2);
% 计算拟合信号
fitted_signal = polyval(p, sinEL_strict);
% 计算原始信号减掉拟合信号
residual_signal = (x.*desvio_x) - fitted_signal;
% 绘制原始信号减掉拟合信号
plot(sinEL_strict, residual_signal, 'g', 'LineWidth', 1);
hold on
plot(sinEL_strict, sum(modes(:, 3:5), 2), 'r', 'LineWidth', 1);
% set(gca, 'XTickLabel', []); % 不显示当前子图的横坐标刻度
% 获取当前坐标轴范围
ax = gca;
xlims = ax.XLim;
ylims = ax.YLim;

% 计算图例位置（X 轴中间，Y 轴最小值位置）
x_center = mean(xlims);
y_min = ylims(1);

% 调整图例位置，这里的偏移量可根据实际情况调整
legend_x_pos = 0.40 
legend_y_pos = 0.28
% 添加图例并设置为一行显示且无边框，同时设置位置
h = legend('SNR r ', 'IMF3+IMF4+IMF5', 'Orientation', 'horizontal', 'Box', 'off','FontSize',12);
pos = get(h, 'Position');
pos(1) = legend_x_pos;
pos(2) = legend_y_pos;
set(h, 'Position', pos);
ax.FontSize = 12; % 你可以根据需要修改这个值
% 添加标题和坐标轴标签
% title('原始信号减掉拟合信号与 IMF4');
xlabel('sinEL','FontSize',12);
ylabel('SNR(VOLTS)','FontSize',12);
                   % ===== 新增图：横轴为频率向量，纵轴为 H = 2 - 0.1903 * 频率 / 2 =====
            catch ME
                warning('处理工作表 %s 时发生错误: %s', sheet_name, ME.message);
                continue;
            end
        end
    catch ME
        warning('处理文件 %s 时发生错误: %s', filename, ME.message);
    end
end

% ===== 辅助函数（保持不变）=====
function x = makeStrictlyIncreasing(x, minDelta)
    for i = 2:length(x)
        if x(i) <= x(i-1)
            requiredDelta = x(i-1) - x(i) + minDelta;
            x(i) = x(i) + requiredDelta;
        end
    end
end

function timeStr = convertTime(timeVal)
    if isnumeric(timeVal)
        timeStr = string(datestr(datetime(timeVal, 'ConvertFrom', 'datenum'), 'yyyy-mm-dd HH:MM:SS'));
    elseif isdatetime(timeVal)
        timeStr = string(datestr(timeVal, 'yyyy-mm-dd HH:MM:SS'));
    else
        error('不支持的时间格式');
    end
end