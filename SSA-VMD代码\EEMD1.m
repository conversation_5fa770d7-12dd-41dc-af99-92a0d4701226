clc; clear; close all;
warning('off', 'MATLAB:table:ModifiedVarnames');

%% 添加并行池配置
if isempty(gcp('nocreate'))
    parpool; % 自动检测可用核心数
end

%% 批量处理参数配置
input_folder = 'D:\ga-lstm\p351';
output_root = 'D:\ga-lstm\p351';
min_delta = 1e-9;

%% EEMD参数配置
eemd_params.num_ensemble = 100;
eemd_params.noise_width = 0.2;

%% 初始化环境
if ~exist(output_root, 'dir')
    mkdir(output_root);
end
excel_files = dir(fullfile(input_folder, '*.xlsx'));

%% 并行处理循环
parfor fileIdx = 1:length(excel_files)
    current_filename = fullfile(input_folder, excel_files(fileIdx).name);
    [~, workbook_name, ~] = fileparts(current_filename);
    output_folder = fullfile(output_root, workbook_name);

    fprintf('\n开始处理工作簿: %d/%d | 工作簿名: %s\n', fileIdx, length(excel_files), workbook_name);

    if ~exist(output_folder, 'dir')
        mkdir(output_folder);
    end
    [~, sheetList] = xlsfinfo(current_filename);
    results = struct('SheetName', {}, 'MainFreq', {}, 'StartTime', {}, 'EndTime', {}, 'MedianAZ', {});

    %% 工作表处理循环
    for sheetIdx = 1:length(sheetList)
        currentSheet = sheetList{sheetIdx};
        fprintf('  处理工作表: %d/%d | 工作表名: %s\n', sheetIdx, length(sheetList), currentSheet);

        try 
            %% 数据读取（新增 AZ）
            opts = detectImportOptions(current_filename, 'Sheet', currentSheet);
            opts.SelectedVariableNames = {'TIME', 'EL', 'SNR', 'AZ'};  % 新增 AZ
            rawData = readtable(current_filename, opts);

            %% 数据预处理（新增 az_sorted）
            [sinEL, snr_volts, time_valid, az_sorted] = preprocessData(...
                rawData.TIME, rawData.EL, rawData.SNR, rawData.AZ);  % 新增 AZ

            start_time = min(time_valid);
            end_time = max(time_valid);

            %% 核心分析
            [zhupin, filtered_signal, sinEL_sorted, imfs, residue] = coreAnalysisEEMD(...
                sinEL, snr_volts, min_delta, eemd_params);

            %% 计算 MedianAZ（AZ 首尾值中位数）
            if ~isempty(az_sorted)
                az_first_last = [az_sorted(1), az_sorted(end)];
                median_az = median(az_first_last);
            else
                median_az = NaN;
            end

            %% 结果存储（新增 MedianAZ）
            results(sheetIdx) = struct( ...
                'SheetName', currentSheet, ...
                'MainFreq', zhupin, ...
                'StartTime', start_time, ...
                'EndTime', end_time, ...
                'MedianAZ', median_az ...  % 新增字段
            );

            %% 生成图表
            if ~isempty(sinEL_sorted) && ~isempty(filtered_signal)
                generateSpectrumPlot(currentSheet, sinEL_sorted, filtered_signal, snr_volts, output_folder);
            end
            if ~isempty(imfs) && ~isempty(residue)
                generateEMDPlot(currentSheet, imfs, residue, sinEL_sorted, output_folder);
            end

        catch ME 
            logError(output_folder, currentSheet, ME.message); 
            results(sheetIdx) = struct( ...
                'SheetName', currentSheet, ...
                'MainFreq', NaN, ...
                'StartTime', NaT, ...
                'EndTime', NaT, ...
                'MedianAZ', NaN ...
            );
        end 
    end 

    %% 导出汇总表
    exportWorkbookSummary(results, output_folder, workbook_name);
    fprintf('  工作簿处理完成: %s\n', workbook_name);
end 

%% 数据预处理函数（新增 AZ 输出）
function [sinEL, snr_volts, time_valid, az_sorted] = preprocessData(TIME, EL, SNR, AZ)
    if ~isdatetime(TIME)
        try
            TIME = datetime(TIME, 'ConvertFrom', 'excel');
        catch
            error('时间列格式无法解析');
        end
    end
    validIdx = ~isnat(TIME) & ~isnan(EL) & ~isnan(SNR) & ~isnan(AZ);  % 新增 AZ 条件
    if sum(validIdx) < 2
        error('有效数据不足（至少需要2个点）');
    end
    sinEL = sind(EL(validIdx));
    snr_volts = 10.^(SNR(validIdx)/20);
    time_valid = TIME(validIdx);
    az_sorted = AZ(validIdx);  % 原始 AZ 数据

    % 统一排序处理
    [sinEL, sortIdx] = sort(sinEL);
    snr_volts = snr_volts(sortIdx);
    time_valid = time_valid(sortIdx);
    az_sorted = az_sorted(sortIdx);  % 同步排序
end

%% 核心分析函数（保持不变）
function [zhupin, filtered_signal, sinEL_sorted, imfs, residue] = coreAnalysisEEMD(...
    sinEL, snr_volts, min_delta, eemd_params)

    zhupin = NaN;
    filtered_signal = [];
    sinEL_sorted = [];
    imfs = [];
    residue = [];

    try
        %% 严格递增处理
        sinEL_sorted = makeStrictlyIncreasing(sinEL, min_delta);

        %% 验证数据排序
        assert(issorted(sinEL_sorted), 'sinEL必须严格递增排序');
        assert(all(diff(sinEL_sorted) > 0), 'sinEL必须严格递增');

        %% EEMD分解原始信号
        [imfs, residue] = custom_eemd(snr_volts, eemd_params.num_ensemble, eemd_params.noise_width);

        %% 筛选有效IMF分量
        if isempty(imfs)
            error('EEMD分解未产生有效IMF分量');
        end

        % 计算每个IMF与总和的相关系数
        sum_imfs = sum(imfs, 2);
        correlations = arrayfun(@(i) corr(imfs(:,i), sum_imfs), 1:size(imfs,2));
        valid_imfs = imfs(:, correlations > 0.5);

        %% 重构信号
        if isempty(valid_imfs)
            filtered_signal = sum_imfs; % 使用全部IMF
        else
            filtered_signal = sum(valid_imfs, 2);
        end

        %% 频谱分析
        f_custom = 0:0.1:400;
        [P, f] = plomb(filtered_signal, sinEL_sorted, f_custom);
        [~, maxIdx] = max(P);
        zhupin = f(maxIdx);

    catch ME
        disp(['分析错误: ' ME.message]);
    end
end

%% 严格递增处理函数（保持不变）
function x = makeStrictlyIncreasing(x, minDelta)
    for i = 2:length(x)
        if x(i) <= x(i-1)
            requiredDelta = x(i-1) - x(i) + minDelta;
            x(i) = x(i) + requiredDelta;
        end
    end
end

%% 自定义EEMD实现（保持不变）
function [imfs, residue] = custom_eemd(signal, num_ensemble, noise_width)
    if nargin < 2, num_ensemble = 100; end
    if nargin < 3, noise_width = 0.2; end

    n = length(signal);
    signal_std = std(signal);
    noise_std = noise_width * signal_std;

    max_imfs = 0;
    imfs_ensemble = cell(num_ensemble, 1);
    residue_ensemble = zeros(n, num_ensemble);

    for i = 1:num_ensemble
        noisy_signal = signal + noise_std * randn(size(signal));
        [current_imfs, current_residue] = emd(noisy_signal, 'Interpolation', 'pchip');
        max_imfs = max(max_imfs, size(current_imfs, 2));
        imfs_ensemble{i} = current_imfs;
        residue_ensemble(:,i) = current_residue;
    end

    imfs = zeros(n, max_imfs);
    for j = 1:max_imfs
        imf_avg = zeros(n, 1);
        count = 0;
        for i = 1:num_ensemble
            if size(imfs_ensemble{i}, 2) >= j
                imf_avg = imf_avg + imfs_ensemble{i}(:,j);
                count = count + 1;
            end
        end
        if count > 0
            imfs(:,j) = imf_avg / count;
        else
            imfs(:,j) = 0;
        end
    end

    residue = mean(residue_ensemble, 2);
end

%% 频谱图生成函数（保持不变）
function generateSpectrumPlot(sheetName, sinEL_sorted, filtered_signal, original_signal, output_folder)
    fig = figure('Visible', 'off', 'Position', [100 100 900 800]);

    subplot(3,1,1)
    plot(sinEL_sorted, original_signal, 'b', 'LineWidth', 1)
    title(sprintf('%s 原始信号', sheetName), 'Interpreter', 'none')
    xlabel('sin(EL)'), ylabel('Amplitude (V)')
    grid on
    xlim([min(sinEL_sorted) max(sinEL_sorted)])

    subplot(3,1,2)
    plot(sinEL_sorted, filtered_signal, 'r', 'LineWidth', 1)
    title(sprintf('%s 重构信号', sheetName), 'Interpreter', 'none')
    xlabel('sin(EL)'), ylabel('Amplitude (V)')
    grid on
    xlim([min(sinEL_sorted) max(sinEL_sorted)])

    subplot(3,1,3)
    f_custom = 0:0.1:400;
    [P, f] = plomb(filtered_signal, sinEL_sorted, f_custom);
    plot(f, P, 'k', 'LineWidth', 1)
    xlim([0 400])
    xlabel('Frequency (Hz)')
    ylabel('Power')
    grid on

    set(findall(fig, 'Type', 'axes'), 'FontSize', 9)
    set(fig, 'Color', 'w')
    saveas(fig, fullfile(output_folder, [sheetName '_频谱分析.png']));
    close(fig);
end

%% EMD可视化函数（保持不变）
function generateEMDPlot(sheetName, imfs, residue, sinEL_sorted, output_folder)
    fig = figure('Visible', 'off', 'Position', [100, 100, 800, 600]);
    numIMFs = size(imfs, 2);

    for i = 1:numIMFs
        subplot(numIMFs+1, 1, i);
        plot(sinEL_sorted, imfs(:,i), 'LineWidth', 0.5);
        ylabel(['IMF ', num2str(i)]);
        grid on;
    end

    subplot(numIMFs+1, 1, numIMFs+1);
    plot(sinEL_sorted, residue, 'LineWidth', 0.5);
    ylabel('Residue');
    xlabel('sin(EL)');
    grid on;

    sgtitle([sheetName ' - EEMD分解结果'], 'Interpreter', 'none');
    saveas(fig, fullfile(output_folder, [sheetName '_EEMD分解.png']));
    close(fig);
end

%% 错误日志函数（保持不变）
function logError(folder, sheet, msg)
    fid = fopen(fullfile(folder, 'error_log.txt'),  'a');
    fprintf(fid, '[%s] %s: %s\n', datestr(now, 'yyyy-mm-dd HH:MM'), sheet, msg);
    fclose(fid);
end

%% 汇总表导出函数（新增 MedianAZ）
function exportWorkbookSummary(results, folder, workbook_name)
    sheetNames = {results.SheetName}';
    mainFreqs = [results.MainFreq]';
    startTimes = {results.StartTime}';
    endTimes = {results.EndTime}';
    medianAZs = [results.MedianAZ]';  % 新增字段

    T = table(sheetNames, mainFreqs, startTimes, endTimes, medianAZs, ...
        'VariableNames', {'Sheet', 'MainFrequency', 'StartTime', 'EndTime', 'MedianAZ'});

    writetable(T, fullfile(folder, sprintf('%s_频率汇总表.csv', workbook_name)));
end