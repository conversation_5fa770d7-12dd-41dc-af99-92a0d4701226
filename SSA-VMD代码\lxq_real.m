% liyuan=umit{:,"liyuan"};
gaodujaio=umit{:,"EL"};
xinzaobi=umit{:,"SNR"};
% fangweijiao=umit{:,"AZ"};
% 读取数据
figure(1)
snr_volts_1=10.^(xinzaobi./20);
% plot(liyuan,snr_volts_1);
title('历元，SNR功率图+拟合曲线图');
% p = polyfit(liyuan,snr_volts_1,5);
% xinzaobi_fit = polyval(p,liyuan);
% z_diff=snr_volts_1-xinzaobi_fit

hold on; % 保持当前图形，以便添加新的曲线figure(2)
plot(liyuan,xinzaobi_fit, 'r'); % 绘制拟合曲线，这里用红色表示
hold off; % 释放当前图形% 


figure(2)
degree = gaodujaio * (pi/180);
c=sin(degree)
% plot(c,z_diff)
hold on; % 保持当前图形，以便添加新的曲线figure(3)
plot(c,snr_volts_1)
hold off
title('sinE，dsnr图');
figure(3)
cc=sin(degree);
plot(liyuan,z_diff,"red")
title('历元，dsnr图');
hold off; % 释放当前图形% 


% 准备时间序列数据

% 计算Lomb光谱
% double(data.EL)
% z_dif=double(z_diff)
[P, f] = plomb(z_diff, makeStrictlyIncreasing(cc)); % 添加函数调用 
figure(4);
% 绘制幅度谱图
plot(f, P);
xlim([0,400])
xlabel('频率');
ylabel('幅度谱');
title('Lomb分析结果');

% 找到主要频率分量
[maxP, maxIdx] = max(P);
mainFreq = f(maxIdx);
fprintf('主要频率分量为 %.2f\n', mainFreq);
% ======== 严格递增处理函数（必须置于文件末尾）======== 
function x = makeStrictlyIncreasing(x)
    minDelta = 1e-9; % 确保时间序列严格递增的最小修正量 
    for i = 2:length(x)
        if x(i) <= x(i-1)
            requiredDelta = x(i-1) - x(i) + minDelta;
            x(i) = x(i) + requiredDelta;
        end 
    end 
end 
