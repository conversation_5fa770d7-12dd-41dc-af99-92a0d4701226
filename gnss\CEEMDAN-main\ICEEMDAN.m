% ===== 配置参数 =====
input_folder = 'D:\excelsnow\P3512021KECHULI';  % 输入文件夹路径
output_base = 'D:\excelsnow\P3512021ICEEMDAN';          % 输出基础目录

% 初始化并行池（根据CPU核心数自动选择）
pool = gcp('nocreate');
if isempty(pool)
    pool = parpool();  % 自动检测可用核心数
end

% 获取所有Excel文件（包含.xls和.xlsx）
file_list = dir(fullfile(input_folder, '*.xls*'));

% 并行处理所有Excel文件
parfor file_idx = 1:numel(file_list)
    try
        input_file = fullfile(file_list(file_idx).folder, file_list(file_idx).name);
        [~, filename, ~] = fileparts(input_file);
        fprintf('并行处理文件: %s (%d/%d)\n', filename, file_idx, numel(file_list));

        % 创建文件专属输出目录
        file_output_dir = fullfile(output_base, filename);
        if ~exist(file_output_dir, 'dir')
            mkdir(file_output_dir);
        end

        % ===== 批量处理所有工作表 =====
        try
            [~, sheets] = xlsfinfo(input_file);
        catch ME
            warning('无法读取文件 %s: %s', input_file, ME.message);
            continue;
        end

        for sheet_idx = 1:numel(sheets)
            sheet_name = sheets{sheet_idx};
            fprintf('  处理工作表: %s (%d/%d)\n', sheet_name, sheet_idx, numel(sheets));

            % 读取当前工作表数据（保持原有逻辑）
            try
                data = readtable(input_file, 'Sheet', sheet_name);
            catch ME
                warning('    跳过工作表 %s: %s', sheet_name, ME.message);
                continue;
            end

            % 尝试处理工作表，如果出错则跳过
            try
                % ===== 数据预处理（保持原有逻辑不变）=====
                TIME = data.TIME;
                SNR = data.SNR_dBHz;
                EL = data.EL_deg;

                % 强制转换时间列为数值型（datenum格式）
                if ~isnumeric(TIME)
                    try
                        TIME = datenum(TIME);  % 自动尝试转换
                    catch
                        TIME = datenum(datetime(TIME, 'InputFormat', 'yyyy-MM-dd HH:mm:ss'));
                    end
                end

                % 计算sinEL并转换为弧度
                sinEL = sind(EL);        % 直接计算度数正弦值
                % 对sinEL进行递增排序，并同步调整TIME和SNR
                [sinEL_sorted, idx] = sort(sinEL);
                TIME_sorted = TIME(idx);
                SNR_sorted_1 = SNR(idx);
                SNR_sorted = 10.^(SNR_sorted_1/20);
                % 确保sinEL严格递增（保持原始数据趋势）
                minDelta = 1e-6;  % 最小增量，避免数值问题
                sinEL_strict = makeStrictlyIncreasing(sinEL_sorted, minDelta);

                % 标准化处理后的SNR
                SNR = SNR_sorted;  % 使用排序后的SNR
                SNR = SNR(:);           % 确保为列向量
                desvio_x = std(SNR);   % 计算原始信号标准差
                SNR = SNR / desvio_x;  % 标准化

                % ===== 参数设置 =====
                Nstd = 0.2;     % 噪声强度系数（典型值0.1-0.4）
                NR = 100;       % 噪声实现次数（典型值50-200）
                MaxIter = 1000; % 最大迭代次数
                SNRFlag = 2;    % 噪声标准化标志（1=不标准化，2=标准化）

                % ===== 初始化变量 =====
                x = SNR;
                num_samples = length(x);
                modes = zeros(num_samples, 0);  % 存储IMF的列矩阵
                residue = x;                    % 初始残差
                aux = zeros(num_samples, 1);    % 辅助变量
                iter = zeros(NR, round(log2(num_samples) + 5));

                % ===== 生成高斯白噪声 =====
                white_noise = cell(NR, 1);
                for i = 1:NR
                    white_noise{i} = randn(num_samples, 1);  % 列向量噪声
                end

                % ===== 预分解噪声的IMF =====
                noise_modes = cell(NR, 1);
                for i = 1:NR
                    noise_modes{i} = emd(white_noise{i});  % 每个噪声的IMF矩阵
                end

                % ===== 主分解循环 =====
                k = 1;
                while true
                    % 生成带噪信号
                    if k == 1
                        % 第一个IMF的特殊处理
                        for i = 1:NR
                            % 获取噪声的第一个IMF并标准化
                            imf_noise = noise_modes{i}(1, :)';  % 转置为列
                            imf_noise = imf_noise / std(imf_noise);

                            % 构造带噪信号
                            noisy_signal = residue + Nstd * imf_noise;

                            % EMD分解
                            [temp, ~, it] = emd(noisy_signal, 'MAXMODES', 1, 'MAXITERATIONS', MaxIter);
                            imf = temp(1, :)';  % 提取第一个IMF并转置

                            % 更新辅助变量
                            aux = aux + (noisy_signal - imf) / NR;
                            iter(i, k) = it;
                        end
                    else
                        % 后续IMF的通用处理
                        for i = 1:NR
                            % 获取对应阶数的噪声IMF
                            if size(noise_modes{i}, 1) >= k
                                imf_noise = noise_modes{i}(k, :)';  % 转置为列
                            else
                                imf_noise = zeros(num_samples, 1);  % 不足时补零
                            end

                            % 调整噪声强度
                            if SNRFlag == 2
                                imf_noise = imf_noise / std(imf_noise);
                            end
                            imf_noise = Nstd * imf_noise;

                            % 构造带噪信号
                            noisy_signal = residue + std(residue) * imf_noise;

                            % ICEEMDAN分解（这里是主要修改点）
                            [temp, ~ ] = emd(noisy_signal + 0.2 * imf_noise, 'MAXMODES', 1, 'MAXITERATIONS', MaxIter);
                            imf = temp(end, :)';  % 提取最后一个IMF并转置

                            % 更新辅助变量
                            aux = aux + imf / NR;
                            iter(i, k) = it;
                        end
                    end

                    % 保存当前IMF
                    current_imf = residue - aux;
                    modes = [modes, current_imf];  % 列追加模式

                    % 更新残差
                    residue = aux;
                    aux = zeros(num_samples, 1);  % 重置辅助变量

                    % 终止条件检查
                    if size(emd(residue, 'MAXMODES', 1, 'MAXITERATIONS', MaxIter), 1) <= 1
                        break;
                    end
                    k = k + 1;
                end

                % 添加最终残差
                modes = [modes, residue];

                % ===== 后处理 =====
                modes = modes * desvio_x;  % 恢复原始尺度

                % 创建工作表专属输出目录
                sheet_output_dir = fullfile(file_output_dir, sheet_name);
                if ~exist(sheet_output_dir, 'dir')
                    mkdir(sheet_output_dir);
                end

                % ===== 生成ICEEMDAN分解图 =====
                fig_decomposition = figure('Visible', 'off', 'Color', 'w');

                % 绘制原始信号
                subplot(size(modes,2)+1, 1, 1);
                plot(sinEL_strict, x*desvio_x, 'b', 'LineWidth', 1);
                ylabel('原始信号');
                title(sprintf('ICEEMDAN分解结果 - %s', sheet_name));
                grid on;

                % 绘制所有IMF分量
                for i = 1:size(modes,2)-1
                    subplot(size(modes,2)+1, 1, i+1);
                    plot(sinEL_strict, modes(:,i), 'LineWidth', 0.8);
                    ylabel(['IMF ', num2str(i)]);
                    grid on;
                end

                % 绘制残差
                subplot(size(modes,2)+1, 1, size(modes,2)+1);
                plot(sinEL_strict, modes(:,end), 'r', 'LineWidth', 1.5);
                ylabel('残差');
                xlabel('sinEL');
                grid on;

                % 调整子图间距
                set(fig_decomposition, 'Position', [100, 100, 800, 1200]);

                % 保存分解图
                decomposition_file = fullfile(sheet_output_dir, sprintf('%s_ICEEMDAN分解图.png', sheet_name));
                try
                    exportgraphics(fig_decomposition, decomposition_file, 'Resolution', 300);
%                     fprintf('ICEEMDAN分解图保存成功: %s\n', decomposition_file);
                catch ME
                    warning('ICEEMDAN分解图保存失败: %s\n错误信息: %s', decomposition_file, ME.message);
                end
                close(fig_decomposition);

                % ===== 频谱分析并保存图像 =====
                imf_sum = sum(modes(:, 1:end-1), 2);  % 所有IMF的和（排除残差）
                correlations = zeros(1, size(modes, 2)-1);

                % 计算每个IMF与总和的相关系数
                for i = 1:size(modes, 2)-1
                    correlations(i) = corr(modes(:, i), imf_sum);
                end
                % 筛选相关系数>0.5的IMF并合并
                selected_imfs = modes(:, correlations > 0.4);
                first_component = sum(selected_imfs, 2);
                % 使用自定义频率范围进行频谱分析
                f_custom = 0:0.1:400;
                [P_first, f_first] = plomb(first_component, sinEL_strict, f_custom);
                [~, maxIdx_first] = max(P_first);
                zhupin_first = f_first(maxIdx_first);  % 合并信号主频

                % 创建专用图形对象
                fig = figure('Visible', 'off', 'Color', 'w');  % 保持不可见模式

                % 绘制频谱图（上方）
                subplot(2,1,1);
                plot(f_first, P_first);
                xlabel('频率 (Hz)');
                ylabel('功率');
                title(sprintf('合并信号频谱 (主频: %.2f Hz)', zhupin_first));
                xlim([f_custom(1), f_custom(end)]);
                grid on;

                % 绘制合成信号（下方）
                subplot(2,1,2);
                plot(sinEL_strict, first_component);
                xlabel('sinEL');
                ylabel('幅度');
                title('合成信号（IMF合并结果）');
                xlim([min(sinEL_strict), max(sinEL_strict)]);
                grid on;

                % 强制刷新图形
                drawnow;

                % 保存图像（使用高分辨率矢量格式）
                spectrum_file = fullfile(sheet_output_dir, sprintf('%s_频谱图.png', sheet_name));
                try
                    saveas(gcf, spectrum_file);  % 使用传统saveas保持兼容性

%                     fprintf('图像保存成功: %s 和 %s\n', decomposition_file, spectrum_file);
                catch ME
                    warning('图像保存失败: %s\n错误信息: %s', decomposition_file, ME.message);
                end

                close(fig);  % 关闭图形对象

                % ===== 收集汇总数据 =====
                % 获取时间范围
                start_time = min(TIME_sorted);
                end_time = max(TIME_sorted);

                % 创建汇总记录
                new_record = table(zhupin_first, ...
                    string(convertTime(start_time)), ...
                    string(convertTime(end_time)), ...
                    string(sheet_name), ...
                    'VariableNames', {'MainFrequency_Hz', 'StartTime', 'EndTime', 'SourceSheet'});

                % 保存当前工作表的汇总表
                summary_file = fullfile(sheet_output_dir, sprintf('%s_频率汇总表.csv', sheet_name));
                writetable(new_record, summary_file);
%                 fprintf('汇总表已保存: %s\n', summary_file);

            catch ME
                warning('处理工作表 %s 时发生错误: %s', sheet_name, ME.message);
                continue;
            end
        end
    catch ME
        warning('处理文件 %s 时发生错误: %s', filename, ME.message);
    end
end

% 关闭并行池（可选，保留池可加速后续并行操作）
% delete(pool);

% ===== 辅助函数（保持不变）=====
function x = makeStrictlyIncreasing(x, minDelta)
    for i = 2:length(x)
        if x(i) <= x(i-1)
            requiredDelta = x(i-1) - x(i) + minDelta;
            x(i) = x(i) + requiredDelta;
        end
    end
end

function timeStr = convertTime(timeVal)
    if isnumeric(timeVal)
        timeStr = string(datestr(datetime(timeVal, 'ConvertFrom', 'datenum'), 'yyyy-mm-dd HH:MM:SS'));
    elseif isdatetime(timeVal)
        timeStr = string(datestr(timeVal, 'yyyy-mm-dd HH:MM:SS'));
    else
        error('不支持的时间格式');
    end
end