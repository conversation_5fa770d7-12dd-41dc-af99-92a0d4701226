import numpy as np
import matplotlib.pyplot as plt
import hdbscan
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import IsolationForest
import os
from openpyxl import load_workbook
from openpyxl.styles import PatternFill
import warnings
from datetime import datetime

# 过滤警告
warnings.filterwarnings("ignore", category=FutureWarning)

# 设置中文显示
plt.rcParams["font.family"] = ["SimHei", "Microsoft YaHei", "DejaVu Sans", "Arial Unicode MS", "sans-serif"]
plt.rcParams["axes.unicode_minus"] = False

def load_data_by_date(file_path, target_column, time_column=None, date_column=None):
    """按日期分组加载数据"""
    df = pd.read_excel(file_path)
    
    # 如果提供了日期列，则按日期分组
    if date_column and date_column in df.columns:
        # 假设日期列是时间格式，提取日期部分
        df['_date_group'] = pd.to_datetime(df[date_column]).dt.date
        grouped_data = {}
        
        for date, group in df.groupby('_date_group'):
            data = group[target_column].values.reshape(-1, 1)
            time = group[time_column].values if time_column else np.arange(len(data))
            grouped_data[date] = {
                'df': group,
                'data': data,
                'time': time
            }
        return grouped_data
    else:
        # 如果没有日期列，按原来的方式处理
        data = df[target_column].values.reshape(-1, 1)
        time = df[time_column].values if time_column else np.arange(len(data))
        return {'all_data': {'df': df, 'data': data, 'time': time}}

def detect_outliers(data, min_cluster_size=50, min_samples=50):
    """两阶段异常值检测"""
    # 确保数据是二维数组
    data = np.asarray(data).reshape(-1, 1)
    
    # 检查数据量，如果数据量少于默认参数，则调整参数
    n_points = len(data)
    if n_points < 10:  # 如果数据点太少，直接返回
        print(f"  警告: 数据点过少 ({n_points})，跳过异常检测")
        return np.zeros(n_points, dtype=bool), np.zeros(n_points), np.full(n_points, -1)
    
    # 自适应调整参数
    adjusted_min_cluster_size = min(min_cluster_size, max(2, n_points // 10))
    adjusted_min_samples = min(min_samples, max(2, n_points // 10))
    
    if adjusted_min_cluster_size != min_cluster_size or adjusted_min_samples != min_samples:
        print(f"  调整参数: min_cluster_size {min_cluster_size}->{adjusted_min_cluster_size}, "
              f"min_samples {min_samples}->{adjusted_min_samples}")
    
    # 第一阶段：HDBSCAN检测
    scaler = StandardScaler()
    data_scaled = scaler.fit_transform(data)
    
    try:
        clusterer = hdbscan.HDBSCAN(
            min_cluster_size=adjusted_min_cluster_size,
            min_samples=adjusted_min_samples,
            allow_single_cluster=True,
            prediction_data=True
        )
        clusterer.fit(data_scaled)
        hdbscan_scores = clusterer.outlier_scores_
        hdbscan_labels = clusterer.labels_
        
        # 如果HDBSCAN失败或没有产生分数，则使用默认值
        if hdbscan_scores is None:
            hdbscan_scores = np.zeros(len(data))
    except Exception as e:
        print(f"  HDBSCAN检测失败: {str(e)}，使用默认值")
        hdbscan_scores = np.zeros(len(data))
        hdbscan_labels = np.full(len(data), -1)
    
    # 第二阶段：Isolation Forest专注小数值检测
    data_flat = data.flatten()  # 转换为1维数组方便操作
    small_value_mask = (data_flat < 10)  # 找出小数值
    
    if np.any(small_value_mask) and np.sum(small_value_mask) > 10:  # 确保有足够的小数值点
        try:
            iso_forest = IsolationForest(
                contamination=min(0.2, 0.5),  # 预期异常值比例
                random_state=42
            )
            # 输入必须是二维，所以reshape
            small_values = data[small_value_mask].reshape(-1, 1)
            iso_scores = iso_forest.fit_predict(small_values)
            
            # 合并结果
            combined_outlier = np.zeros(len(data_flat), dtype=bool)
            
            # 对大数值应用HDBSCAN
            if np.any(~small_value_mask):
                hdbscan_threshold = np.percentile(
                    hdbscan_scores[~small_value_mask], 90)
                combined_outlier[~small_value_mask] = (
                    hdbscan_scores[~small_value_mask] > hdbscan_threshold)
            
            # 对小数值应用Isolation Forest
            combined_outlier[small_value_mask] = (iso_scores == -1)
        except Exception as e:
            print(f"  Isolation Forest检测失败: {str(e)}，仅使用HDBSCAN结果")
            combined_outlier = hdbscan_scores > np.percentile(hdbscan_scores, 95)
    else:
        combined_outlier = hdbscan_scores > np.percentile(hdbscan_scores, 95) if np.any(hdbscan_scores) else np.zeros(len(data_flat), dtype=bool)
    
    return combined_outlier, hdbscan_scores, hdbscan_labels

def detect_outliers_by_date(grouped_data, min_cluster_size=50, min_samples=50):
    """按日期分别进行异常值检测"""
    results_by_date = {}
    
    for date, data_dict in grouped_data.items():
        print(f"正在处理日期: {date} (数据点数: {len(data_dict['data'])})")
        data = data_dict['data']
        
        # 对每一天的数据单独进行异常值检测
        is_outlier, outlier_scores, labels = detect_outliers(
            data, min_cluster_size, min_samples
        )
        
        results_by_date[date] = {
            'is_outlier': is_outlier,
            'outlier_scores': outlier_scores,
            'labels': labels,
            'data': data,
            'time': data_dict['time'],
            'df': data_dict['df']
        }
    
    return results_by_date

def save_all_results_to_single_excel(results_by_date, target_column, output_file):
    """将所有结果保存到同一张Excel表中，并将异常值用红色标记"""
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")
    
    # 合并所有日期的数据
    all_data = []
    for date, result in results_by_date.items():
        df = result['df'].copy()
        is_outlier = result['is_outlier']
        outlier_scores = result['outlier_scores']
        
        # 添加结果列
        df['Date'] = date
        df['Outlier'] = is_outlier
        df['OutlierScore'] = outlier_scores
        all_data.append(df)
    
    # 合并所有数据
    combined_df = pd.concat(all_data, ignore_index=True)
    
    # 保存到Excel (先不添加格式)
    combined_df.to_excel(output_file, index=False)
    print(f"所有结果已保存到单个Excel文件: {output_file}")
    
    # 高亮显示异常值（用红色）
    try:
        wb = load_workbook(output_file)
        ws = wb.active
        
        # 设置红色填充
        red_fill = PatternFill(start_color='FFFF0000', end_color='FFFF0000', fill_type='solid')  # 红色
        
        # 找到Outlier列 - 改进版
        outlier_col_idx = None
        for col_idx, cell in enumerate(ws[1], 1):  # 从1开始计数列
            if str(cell.value).strip().lower() == 'outlier':
                outlier_col_idx = col_idx
                break
        
        if outlier_col_idx:
            print(f"找到Outlier列，位于第 {outlier_col_idx} 列")
            # 应用颜色到异常值行
            outlier_count = 0
            for row_idx in range(2, ws.max_row + 1):  # 从第2行开始（跳过标题）
                cell_value = ws.cell(row=row_idx, column=outlier_col_idx).value
                # 更全面的异常值判断
                is_outlier = False
                if isinstance(cell_value, bool):
                    is_outlier = cell_value
                elif isinstance(cell_value, str):
                    is_outlier = cell_value.strip().lower() in ['true', '1', 'yes']
                elif isinstance(cell_value, (int, float)):
                    is_outlier = cell_value != 0
                
                if is_outlier:
                    outlier_count += 1
                    # 标记整行
                    for col in range(1, ws.max_column + 1):
                        ws.cell(row=row_idx, column=col).fill = red_fill
            
            print(f"已标记 {outlier_count} 个异常值为红色")
            wb.save(output_file)
            print(f"已为异常值添加红色标记并保存: {output_file}")
        else:
            print("警告: 未找到Outlier列，无法进行颜色标记")
            # 尝试列出所有列名帮助调试
            column_names = [cell.value for cell in ws[1]]
            print(f"Excel中的列名: {column_names}")
            
    except Exception as e:
        print(f"标记异常值时出错: {str(e)}")
        # 保存未标记的文件作为备份
        backup_file = output_file.replace('.xlsx', '_backup.xlsx')
        combined_df.to_excel(backup_file, index=False)
        print(f"已保存未标记的备份文件: {backup_file}")

def plot_results_by_date(results_by_date, output_dir):
    """按日期分别生成可视化结果（仅保存不显示）"""
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")
    
    for date, result in results_by_date.items():
        fig, axes = plt.subplots(3, 1, figsize=(12, 15))
        
        data_flat = result['data'].flatten()
        is_outlier_flat = result['is_outlier'].flatten() if result['is_outlier'].ndim > 1 else result['is_outlier']
        time = result['time']
        outlier_scores = result['outlier_scores']
        
        # 全量数据
        axes[0].scatter(time, data_flat, c=is_outlier_flat, cmap='cool', alpha=0.7)
        axes[0].set_title(f'{date} - 全量数据异常值分布')
        axes[0].set_xlabel('时间')
        axes[0].set_ylabel('数值')
        
        # 小数值区域(0-1)
        mask = (data_flat >= 0) & (data_flat <= 1)
        if np.any(mask):
            axes[1].scatter(time[mask], data_flat[mask], c=is_outlier_flat[mask], cmap='cool', alpha=0.7)
        axes[1].set_title(f'{date} - 0-1范围数值异常值分布')
        axes[1].set_xlabel('时间')
        axes[1].set_ylabel('数值')
        
        # 异常值分数分布
        axes[2].hist(outlier_scores, bins=50, alpha=0.7, color='salmon')
        axes[2].set_title(f'{date} - 异常值分数分布')
        axes[2].set_xlabel('异常值分数')
        axes[2].set_ylabel('频数')
        
        plt.tight_layout()
        plot_file = os.path.join(output_dir, f'results_{date}_plot.png')
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        # 移除了 plt.show() 语句，避免图片显示在屏幕上
        plt.close()  # 关闭图形以释放内存
        print(f"日期 {date} 图表已保存到: {plot_file}")

def highlight_outliers_in_excel(file_path, target_column, outlier_mask):
    """备选的异常值标记函数"""
    try:
        df = pd.read_excel(file_path)
        wb = load_workbook(file_path)
        ws = wb.active
        
        # 找到目标列
        target_col_idx = None
        for col_idx, cell in enumerate(ws[1], 1):
            if str(cell.value).strip() == target_column:
                target_col_idx = col_idx
                break
        
        if target_col_idx:
            red_fill = PatternFill(start_color="FFFF0000", end_color="FFFF0000", fill_type="solid")
            outlier_count = 0
            
            for row_idx, is_outlier in enumerate(outlier_mask, 2):  # 行索引从2开始
                if is_outlier:
                    outlier_count += 1
                    # 只标记目标列的异常值
                    ws.cell(row=row_idx, column=target_col_idx).fill = red_fill
            
            wb.save(file_path)
            print(f"备选方法：已在 {target_column} 列标记 {outlier_count} 个异常值")
        else:
            print(f"备选方法：未找到 {target_column} 列")
            
    except Exception as e:
        print(f"备选标记方法出错: {str(e)}")

if __name__ == "__main__":
    # 参数设置
    data_file = r"D:\ga-lstm\p351pinllv\p351_with_outliers.xlsx"
    output_dir = r"D:\ga-lstm\p351pinllv\daily_results"
    single_output_file = r"D:\ga-lstm\p351pinllv\all_results.xlsx"  # 单个Excel文件路径
    target_column = "MainFrequency"
    time_column = "EndTime"
    date_column = "EndTime"  # 假设使用EndTime列提取日期，可根据实际调整
    
    # 验证文件存在性
    if not os.path.isfile(data_file):
        print(f"错误：文件不存在\n{data_file}")
        exit(1)
    
    try:
        # 按日期加载数据
        print("正在按日期加载数据...")
        grouped_data = load_data_by_date(data_file, target_column, time_column, date_column)
        
        # 显示数据分组信息
        print(f"数据按日期分组完成，共 {len(grouped_data)} 个日期组:")
        for date, data_dict in grouped_data.items():
            print(f"  {date}: {len(data_dict['data'])} 个数据点")
        
        # 按日期分别检测异常值
        print("正在进行按日期异常值检测...")
        results_by_date = detect_outliers_by_date(grouped_data)
        
        # 保存所有结果到单个Excel文件
        print("正在保存所有结果到单个Excel文件...")
        save_all_results_to_single_excel(results_by_date, target_column, single_output_file)
        
        # 生成所有异常值的掩码，用于备选标记方法
        all_outliers = []
        for date, result in results_by_date.items():
            all_outliers.extend(result['is_outlier'])
        
        # 如果主方法失败，尝试备选方法
        highlight_outliers_in_excel(single_output_file, target_column, all_outliers)
        
        # 按日期分别可视化结果
        print("生成按日期可视化结果...")
        plot_results_by_date(results_by_date, output_dir)
        
        print(f"所有结果已保存到单个Excel文件: {single_output_file}")
        
    except Exception as e:
        print(f"程序运行出错: {str(e)}")
        import traceback
        traceback.print_exc()
