clc; clear; close all;
warning('off', 'MATLAB:table:ModifiedVarnames');

%% 参数配置 
filename = 'g01.xlsx';           % 输入文件路径 
output_folder = 'NoTime_Analysis';     % 结果输出目录 
min_delta = 1e-9;                     % 严格递增处理参数 

% SSA 参数
pop_size = 20; % 种群数量
max_iter = 30; % 最大迭代次数
dim = 2; % 优化维度（K 和 alpha）
lb = [3, 1000]; % K 和 alpha 的下限
ub = [8, 5000]; % K 和 alpha 的上限

% 适应度函数权重
weight_envelope_entropy = 0.5;
weight_energy_loss = 0.5;

%% 初始化环境 
if ~exist(output_folder, 'dir')
    mkdir(output_folder);
end 
[~, sheetList] = xlsfinfo(filename);

results = struct('SheetName', {}, 'MainFreq', {}, 'Residuals', {}, 'StartTime', {}, 'EndTime', {}, 'OptimalK', {}, 'OptimalAlpha', {});

%% 主处理循环，逐个处理工作表
for sheetIdx = 1:length(sheetList)
    currentSheet = sheetList{sheetIdx};
    try 
        fprintf('\n开始处理工作表: %s\n', currentSheet);
        
        % 数据读取 (包含时间列)
        opts = detectImportOptions(filename, 'Sheet', currentSheet);
        opts.SelectedVariableNames = {'TIME', 'EL', 'SNR'};  
        rawData = readtable(filename, opts);
        
        % 数据预处理 
        [sinEL, snr_volts, time_valid] = preprocessData(rawData.TIME, rawData.EL, rawData.SNR);
        start_time = min(time_valid);
        end_time = max(time_valid);
        
        % SSA 优化，针对当前工作表的数据
        [best_solution, best_fitness] = ssa_optimization(@(x) objective_function(x, sinEL, snr_volts, min_delta, weight_envelope_entropy, weight_energy_loss), pop_size, max_iter, dim, lb, ub, {currentSheet});
        
        vmd_num_imfs = round(best_solution(1)); % 取整
        vmd_alpha = best_solution(2);
        
        fprintf('工作表 %s 的最优 K 值: %d\n', currentSheet, vmd_num_imfs);
        fprintf('工作表 %s 的最优 alpha 值: %.2f\n', currentSheet, vmd_alpha);
        
        % 核心分析 (使用优化后的 VMD 参数)
        [zhupin, residuals, sinEL_sorted, snr_volts_sorted, imf] = coreAnalysis(sinEL, snr_volts, vmd_num_imfs, vmd_alpha, min_delta);
        
        % 结果存储 
        results(sheetIdx).SheetName = currentSheet;
        results(sheetIdx).MainFreq = zhupin;
        if ~isempty(sinEL_sorted) && ~isempty(residuals)
            results(sheetIdx).Residuals = [sinEL_sorted(:), residuals(:)];
        else
            results(sheetIdx).Residuals = [];
        end
        results(sheetIdx).StartTime = start_time;
        results(sheetIdx).EndTime = end_time;
        results(sheetIdx).OptimalK = vmd_num_imfs;
        results(sheetIdx).OptimalAlpha = vmd_alpha;
        
        % 生成频谱图 
        if ~isempty(sinEL_sorted) && ~isempty(residuals)
            generateSpectrumPlot(currentSheet, sinEL_sorted, residuals, output_folder);
        end
        
        % 保存 VMD 分解效果图
        saveVMDDecompositionPlot(currentSheet, sinEL_sorted, snr_volts_sorted, imf, output_folder);
        
    catch ME 
        logError(output_folder, currentSheet, ME.message); 
        continue
    end 
end 

%% 结果导出 
exportSummary(results, output_folder);
fprintf('\n==== 分析完成 ====\n');

%% 预处理函数 
function [sinEL, snr_volts, time_valid] = preprocessData(Time, EL, SNR)
    % 转换时间格式并过滤无效数据
    if ~isdatetime(Time)
        try
            Time = datetime(Time, 'ConvertFrom', 'excel');
        catch
            error('时间列格式无法解析');
        end
    end
    validIdx = ~isnat(Time) & ~isnan(EL) & ~isnan(SNR);
    if sum(validIdx) < 2
        error('有效数据不足（至少需要2个点）');
    end
    sinEL = sind(EL(validIdx));
    snr_volts = 10.^(SNR(validIdx)/20);
    time_valid = Time(validIdx);
end

%% 核心分析函数（使用VMD分解）
function [zhupin, residuals, sinEL_sorted, snr_volts_sorted, imf] = coreAnalysis(sinEL, snr_volts, num_imfs, alpha, min_delta)
    zhupin = NaN;
    residuals = [];
    sinEL_sorted = [];
    snr_volts_sorted = [];
    try
        if isempty(sinEL) || isempty(snr_volts) || length(sinEL) ~= length(snr_volts)
            error('输入数据不合法');
        end
        
        % VMD分解
        [imf, ~] = vmd(snr_volts(:), 'NumIMFs', num_imfs, 'PenaltyFactor', alpha);
        % 修改residuals的计算方式
        residuals = snr_volts(:) - imf(:, end); % 使用snr减去imf最后一个分量
        
        % 严格递增处理
        [sinEL_sorted, sortIdx] = sort(sinEL);
        sinEL_sorted = makeStrictlyIncreasing(sinEL_sorted, min_delta);
        residuals = residuals(sortIdx); % 同步排序残差
        imf = imf(sortIdx, :); % 同步排序 imf
        snr_volts_sorted = snr_volts(sortIdx); % 同步排序 snr_volts

        % Lomb-Scargle分析
        f_custom = 0:0.1:400;
        [P, f] = plomb(residuals, sinEL_sorted, f_custom);
        
        % 提取主频
        [~, maxIdx] = max(P);
        zhupin = f(maxIdx);
        
    catch ME
        error('分析失败: %s', ME.message);
    end
end

%% 可视化函数 
function generateSpectrumPlot(sheetName, sinEL_sorted, residuals, output_folder)
    fig = figure('Visible', 'off');
    subplot(2,1,1)
    plot(sinEL_sorted, residuals, "red")
    title(sprintf('%s 残差分布', sheetName), 'Interpreter', 'none')
    xlabel('sin(EL)'), ylabel('Residual (V)')
    subplot(2,1,2)
    f_custom = 0:0.1:400;
    [P, f] = plomb(residuals, sinEL_sorted,f_custom);
    xlim([0 400])
    plot(f, P)
    xlabel('Frequency (Hz)')
    ylabel('Power')
    grid on 
    saveas(fig, fullfile(output_folder, sprintf('%s_频谱.png', sheetName)));
    close(fig);
end

%% 辅助函数 
function logError(folder, sheet, msg)
    fid = fopen(fullfile(folder, 'error_log.txt'),  'a');
    fprintf(fid, '[%s] %s: %s\n', datestr(now, 'yyyy-mm-dd HH:MM'), sheet, msg);
    fclose(fid);
end 

%% 导出汇总表函数
function exportSummary(results, folder)
    sheetNames = {results.SheetName}';
    mainFreqs = [results.MainFreq]';
    startTimes = {results.StartTime}';
    endTimes = {results.EndTime}';
    optimalKs = [results.OptimalK]';
    optimalAlphas = [results.OptimalAlpha]';
    T = table(sheetNames, mainFreqs, startTimes, endTimes, optimalKs, optimalAlphas, ...
        'VariableNames', {'Sheet', 'MainFrequency', 'StartTime', 'EndTime', 'OptimalK', 'OptimalAlpha'});
    writetable(T, fullfile(folder, '频率汇总表.csv'));
    save(fullfile(folder, '完整结果.mat'), 'results');
end 

%% 严格递增处理函数
function x = makeStrictlyIncreasing(x, minDelta)
    for i = 2:length(x)
        if x(i) <= x(i-1)
            requiredDelta = x(i-1) - x(i) + minDelta;
            x(i) = x(i) + requiredDelta;
        end
    end
end

%% 保存 VMD 分解效果图函数
function saveVMDDecompositionPlot(sheetName, sinEL_sorted, snr_volts_sorted, imf, output_folder)
    num_imfs = size(imf, 2);
    fig = figure('Visible', 'off');
    
    % 绘制原始数据子图
    subplot(num_imfs + 1, 1, 1);
    plot(sinEL_sorted, snr_volts_sorted,"red");
    title(sprintf('%s - 原始数据', sheetName));
    ylabel('幅值');
    
    % 绘制 IMF 子图
    for i = 1:num_imfs
        subplot(num_imfs + 1, 1, i + 1);
        plot(sinEL_sorted, imf(:, i));
        title(sprintf('IMF %d',  i));
        ylabel('幅值');
    end
    
    saveas(fig, fullfile(output_folder, sprintf('%s_VMD分解.png', sheetName)));
    close(fig);
end

%% 计算包络熵函数
function entropy = envelope_entropy(signal)
    % 计算信号的包络
    env = abs(hilbert(signal));
    
    % 归一化包络
    norm_env = env / sum(env);
    
    % 计算包络熵
    entropy = -sum(norm_env .* log2(norm_env + eps));
end

%% 计算能量损失函数
function energy_loss = calculate_energy_loss(original_signal, imf)
    total_energy_imf = sum(sum(imf.^2));
    total_energy_original = sum(original_signal.^2);
    energy_loss = abs(total_energy_original - total_energy_imf);
end

%% 目标函数
function fitness = objective_function(x, sinEL, snr_volts, min_delta, weight_envelope_entropy, weight_energy_loss)
    num_imfs = round(x(1)); % 确保 K 为整数
    alpha = x(2);
    
    [~, residuals, ~, ~, imf] = coreAnalysis(sinEL, snr_volts, num_imfs, alpha, min_delta);
    
    % 计算包络熵
    envelope_entropy_value = envelope_entropy(residuals);
    
    % 计算能量损失
    energy_loss_value = calculate_energy_loss(snr_volts, imf);
    
    % 结合包络熵和能量损失作为适应度函数
    fitness = weight_envelope_entropy * envelope_entropy_value + weight_energy_loss * energy_loss_value;
end

%% SSA 优化算法
function [best_solution, best_fitness] = ssa_optimization(obj_fun, pop_size, max_iter, dim, lb, ub, sheetList)
    % 初始化种群，确保 K 为整数
    pop = repmat(lb, pop_size, 1) + rand(pop_size, dim) .* repmat(ub - lb, pop_size, 1);
    pop(:, 1) = round(pop(:, 1)); % 对 K 取整
    fitness = zeros(pop_size, 1);
    
    % 计算初始适应度
    for i = 1:pop_size
        fitness(i) = obj_fun(pop(i, :));
    end
    
    % 找到初始最优解
    [best_fitness, best_idx] = min(fitness);
    best_solution = pop(best_idx, :);
    
    % 定义发现者和追随者的比例
    PD = 0.2; % 发现者比例
    SD = 0.1; % 警戒者比例
    PD_num = round(pop_size * PD);
    SD_num = round(pop_size * SD);
    
    % 迭代优化
    for iter = 1:max_iter
        % 排序
        [fitness, sorted_idx] = sort(fitness);
        pop = pop(sorted_idx, :);
        
        % 更新发现者位置
        for i = 1:PD_num
            r2 = rand();
            if r2 < 0.8
                pop(i, :) = pop(i, :) .* exp(-i / (rand() * max_iter));
            else
                pop(i, :) = pop(i, :) + randn() * ones(1, dim);
            end
            % 边界处理
            pop(i, 1) = max(pop(i, 1), lb(1));
            pop(i, 1) = min(pop(i, 1), ub(1));
            pop(i, 2) = max(pop(i, 2), lb(2));
            pop(i, 2) = min(pop(i, 2), ub(2));
            pop(i, 1) = round(pop(i, 1)); % 对 K 取整
            fitness(i) = obj_fun(pop(i, :));
        end
        
        % 更新追随者位置
        for i = PD_num + 1:pop_size
            if i > (pop_size / 2)
                pop(i, :) = randn() .* exp((best_solution - pop(i, :)) / i^2);
            else
                A = randi([-1, 1], 1, dim);
                A = A / norm(A);
                pop(i, :) = best_solution + abs(pop(i, :) - best_solution) .* A;
            end
            % 边界处理
            pop(i, 1) = max(pop(i, 1), lb(1));
            pop(i, 1) = min(pop(i, 1), ub(1));
            pop(i, 2) = max(pop(i, 2), lb(2));
            pop(i, 2) = min(pop(i, 2), ub(2));
            pop(i, 1) = round(pop(i, 1)); % 对 K 取整
            fitness(i) = obj_fun(pop(i, :));
        end
        
        % 更新警戒者位置
        worst_idx = find(fitness == max(fitness), 1);
        for i = 1:SD_num
            if fitness(i) > best_fitness
                pop(i, :) = best_solution + randn() .* abs(pop(i, :) - best_solution);
            elseif i == worst_idx
                pop(i, :) = pop(i, :) + randn() .* (pop(i, :) - lb);
            end
            % 边界处理
            pop(i, 1) = max(pop(i, 1), lb(1));
            pop(i, 1) = min(pop(i, 1), ub(1));
            pop(i, 2) = max(pop(i, 2), lb(2));
            pop(i, 2) = min(pop(i, 2), ub(2));
            pop(i, 1) = round(pop(i, 1)); % 对 K 取整
            fitness(i) = obj_fun(pop(i, :));
        end
        
        % 更新最优解
        [current_best_fitness, current_best_idx] = min(fitness);
        if current_best_fitness < best_fitness
            best_fitness = current_best_fitness;
            best_solution = pop(current_best_idx, :);
        end
        
        % 显示当前迭代信息
        for sheetIdx = 1:length(sheetList)
            currentSheet = sheetList{sheetIdx};
            fprintf('迭代 %d | 处理工作表: %s | K = %d, alpha = %.2f, 最佳适应度 = %.6f\n', iter, currentSheet, round(best_solution(1)), best_solution(2), best_fitness);
        end
    end
end    