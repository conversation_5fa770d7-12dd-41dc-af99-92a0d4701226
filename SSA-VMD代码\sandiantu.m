% 清除潜在变量冲突
clear
colormap jet

% 读取数据
data = readtable('C:\Users\<USER>\Desktop\SSA-VMD代码\COFA2_snow_cover_plot_1_新版_完整.xlsx');

% 提取列数据
y = data{:, 2};
x = data{:, "VMD"};

% 线性回归拟合
p = polyfit(x, y, 1);
y_pred = polyval(p, x);

% 计算拟合曲线的斜率
slope = p(1);

% 计算两组数据的相关系数
corr_coeff = corr(x, y);

% 计算距离
distances = abs(x - y);

% 绘制散点图
figure;
scatter(x, y, 20, distances, 'filled');
ylim([-10,90]);
xlim([-10,90]);

% 反转颜色映射，让距离近的为红色，远的为蓝色
cmap = flip(jet(256));
colormap(cmap);

% 正确设置 colorbar 标签（兼容所有版本）
cb = colorbar;
% ylabel(cb, 'Distance (Closer to Red, Farther to Blue)'); 

% 添加拟合线和 1:1 线
hold on;
h1 = plot(x, y_pred, 'm-', 'LineWidth', 1.5, 'DisplayName', ['斜率: ', num2str(slope)]);
h2 = plot([min(x), max(x)], [min(x), max(x)], 'k--', 'DisplayName', '1:1 Line');

% 创建一个不可见的绘图对象用于添加相关系数信息
h3 = plot(nan, nan, 'DisplayName', ['相关系数R: ', num2str(corr_coeff)]);

% 在 X 轴上方显示 ICEEMDAN
x_center = 70; % 计算 X 轴范围的中心位置
y_min = -8;
text(x_center, y_min, 'VMD', 'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom','FontSize',16);

hold off;

% 添加标签和图例
xlabel('反演值(cm)');
ylabel('实测值(cm)');
% title('Scatter Plot with OLS Fit and 1:1 Line');
hLegend = legend([h1, h2, h3], 'Location', 'best');

% 设置图例文字大小
set(hLegend, 'FontSize', 14); 

% 调整图例整体大小和位置（这里示例性地调整位置和大小，你可按需修改）
newPosition = get(hLegend, 'Position');
newPosition(3) = newPosition(3) * 1; % 宽度变为原来的 1.2 倍
newPosition(4) = newPosition(4) * 1; % 高度变为原来的 1.2 倍
set(hLegend, 'Position', newPosition);

% 调整坐标刻度值大小
set(gca, 'FontSize', 14);

% 调整色带的数字大小
set(cb, 'FontSize', 14);