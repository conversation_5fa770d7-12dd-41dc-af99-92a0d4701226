import numpy as np
import matplotlib.pyplot as plt
from sklearn.datasets import make_moons, make_blobs
from sklearn.cluster import DBSCAN
import hdbscan

# 生成示例数据：两个密度不同的簇 + 一些噪声
X1, _ = make_moons(n_samples=200, noise=0.05)
X2, _ = make_blobs(n_samples=100, centers=[(3, 3)], cluster_std=0.2)
X = np.vstack((X1, X2))

# DBSCAN 聚类
dbscan = DBSCAN(eps=0.3, min_samples=5).fit(X)
labels_db = dbscan.labels_

# HDBSCAN 聚类
hdb = hdbscan.HDBSCAN(min_cluster_size=10).fit(X)
labels_hdb = hdb.labels_

# 绘制对比结果
fig, axes = plt.subplots(1, 2, figsize=(12, 5))

# DBSCAN
axes[0].scatter(X[:, 0], X[:, 1], c=labels_db, cmap='tab10', s=30)
axes[0].set_title("DBSCAN (eps=0.3, min_samples=5)")

# HDBSCAN
axes[1].scatter(X[:, 0], X[:, 1], c=labels_hdb, cmap='tab10', s=30)
axes[1].set_title("HDBSCAN (min_cluster_size=10)")

plt.show()
