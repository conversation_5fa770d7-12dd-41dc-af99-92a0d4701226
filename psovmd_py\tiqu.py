import os
import shutil

# 配置参数
SOURCE_FOLDER = r"D:\ga-lstm\p351\p351vmd"  # 源文件夹路径
TARGET_FOLDER = r"D:\ga-lstm\combined"  # 目标文件夹路径
EXCEL_FILENAME = "频率汇总表.csv"  # 替换为实际文件名

# 创建目标文件夹（如果不存在）
os.makedirs(TARGET_FOLDER, exist_ok=True)

# 遍历所有子文件夹
for root, dirs, files in os.walk(SOURCE_FOLDER):
    for file in files:
        # 检查是否为指定Excel文件
        if file.lower() == EXCEL_FILENAME.lower():
          

            # 获取上级文件夹名称
            parent_folder = os.path.basename(root)
            # 构建新文件名
            new_filename = f"{parent_folder}.csv"
            # 构建完整路径
            source_path = os.path.join(root, file)
            target_path = os.path.join(TARGET_FOLDER, new_filename)
            
            # 处理文件重命名和移动
            try:
                # 检查目标文件是否已存在
                if os.path.exists(target_path):
                    print(f"警告：文件 {new_filename} 已存在，跳过处理")
                    continue
                # 执行重命名并移动
                shutil.move(source_path, target_path)
                print(f"成功处理：{source_path} -> {target_path}")
            except Exception as e:
                print(f"处理失败：{source_path}，错误信息：{str(e)}")

print("处理完成！")