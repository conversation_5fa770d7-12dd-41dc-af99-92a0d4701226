% 配置批量处理路径
input_folder = 'D:\excelsnow\AB332020原数据';          % 输入文件夹
output_folder = 'D:\excelsnow\p3512020kechuli'; % 输出文件夹
% if ~exist(output_folder, 'dir'), mkdir(output_folder); end % 创建输出目录

% 获取所有xlsx文件列表
file_list = dir(fullfile(input_folder, '*.xlsx'));

% 遍历每个文件
for file_idx = 1:size(file_list, 1)
    % 保持原文件处理逻辑不变，仅修改文件路径部分
    filename = fullfile(input_folder, file_list(file_idx).name); % 输入文件完整路径
    output_filename = fullfile(output_folder, [file_list(file_idx).name(1:end-5) '_processed.xlsx']); % 生成输出文件名
    
    % 以下为原代码核心逻辑（仅调整output_filename和新增文件存在性检查）
    data = readtable(filename, 'Sheet', 'Sheet1', 'PreserveVariableNames', true);
    
    if ismember('Time', data.Properties.VariableNames)
        data.Properties.VariableNames{'Time'} = 'TIME';
    end
    
    if ~isdatetime(data.TIME)
        data.TIME = datetime(data.TIME, 'InputFormat', 'yyyy-MM-dd HH:mm:ss');
    end
    
    if iscell(data.SAT)
        sat_values = unique(data.SAT);
    else
        sat_values = unique(data.SAT);
    end
    
    if exist(output_filename, 'file'), delete(output_filename); end
    
    for i = 1:length(sat_values)
        sat = sat_values{i};
        
        sat_data = data( ...
            strcmp(data.SAT, sat) & ...
            ((data.AZ_deg >= 0 & data.AZ_deg <= 360) & ...
             (data.EL_deg >= 5 & data.EL_deg <= 25)), :);
        
        if isempty(sat_data)
            warning('文件%s中SAT=%s 没有符合条件的数据，跳过。', file_list(file_idx).name, sat);
            continue;
        end
        
        sat_sorted = sortrows(sat_data, 'TIME');
        time_diffs = diff(sat_sorted.TIME);
        break_points = find(time_diffs > minutes(10));
        
        starts = [1; break_points + 1];
        ends = [break_points; height(sat_sorted)];
        
        up_count = 0;
        down_count = 0;
        
        for j = 1:length(starts)
            seg_start = starts(j);
            seg_end = ends(j);
            segment = sat_sorted(seg_start:seg_end, :);
            
            el_start = segment.EL_deg(1);
            el_end = segment.EL_deg(end);
            seg_type = '上升';
            if el_end < el_start
                seg_type = '下降';
            end
            
            if strcmp(seg_type, '上升') && up_count >= 2 || ...
               strcmp(seg_type, '下降') && down_count >= 2
                continue;
            end
            
            if strcmp(seg_type, '上升')
                up_count = up_count + 1;
            else
                down_count = down_count + 1;
            end
            
            sheet_name = sprintf('SAT%s_%s弧段%d', sat, seg_type, up_count + down_count);
            
            writetable(segment, output_filename, ...
                'Sheet', sheet_name, ...
                'WriteMode', 'append', ...
                'AutoFitWidth', false);
        end
    end
    
    % 保留原始数据（可选）
    % writetable(data, output_filename, 'Sheet', '原始数据备份', 'WriteMode', 'append');
    
    % 打开文件（可选）
    % winopen(output_filename);
end
    